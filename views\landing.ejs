<!DOCTYPE html>
<html lang="<%= locale %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <meta name="description" content="StreamOnPod - Platform streaming berbasis cloud untuk siaran konten berkelanjutan di berbagai platform. Ubah video Anda menjadi live stream otomatis dengan infrastruktur tingkat enterprise.">
  <meta name="keywords" content="streaming otomatis, cloud streaming, siaran multi-platform, distribusi konten, otomasi live streaming, platform streaming, streaming indonesia">

  <!-- SEO Meta Tags -->
  <% if (typeof seo !== 'undefined' && seo) { %>
    <%- seo.renderMetaTags() %>
  <% } %>

  <!-- Favicon -->
  <link rel="icon" href="/images/streamonpod-logo.png" type="image/png">
  <link rel="apple-touch-icon" href="/images/streamonpod-logo.png">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">

  <!-- JSON-LD Structured Data -->
  <% if (typeof seo !== 'undefined' && seo && seo.renderJsonLd) { %>
    <%- seo.renderJsonLd() %>
  <% } %>
  
  <!-- Custom Styles -->
  <link rel="stylesheet" href="/css/landing.css">
  
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'inter': ['Inter', 'sans-serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif']
        },
        extend: {
          colors: {
            'primary': '#ad6610',
            'secondary': '#8b5209',
            'dark': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '300': '#AFAFAF',
              '200': '#CFCFCF',
              '100': '#E5E5E5',
            }
          }
        }
      }
    }
  </script>
</head>
<body class="bg-dark-900 text-white font-inter">
  
  <!-- Navigation -->
  <nav class="fixed top-0 left-0 right-0 z-50 navbar-blur border-b border-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <img src="/images/streamonpod-logotype.png" alt="StreamOnPod" class="navbar-logo">
        </div>
        
        <!-- Navigation Links -->
        <div class="hidden md:flex items-center space-x-8">
          <a href="#how-it-works" class="text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.how_it_works') %></a>
          <a href="#features" class="text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.features') %></a>
          <a href="#video-tutorial" class="text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.video_tutorial') %></a>
          <a href="#pricing" class="text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.pricing') %></a>
          <a href="#faq" class="text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.faq') %></a>
          <a href="https://t.me/streamonpod_support" target="_blank" class="text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.support') %></a>
        </div>
        
        <!-- Auth Buttons & Language Switcher -->
        <div class="flex items-center space-x-4">
          <!-- Language Switcher -->
          <div class="relative">
            <button id="language-btn" class="group p-2 text-gray-300 hover:text-white transition-all duration-200 flex items-center gap-1">
              <i class="ti ti-language text-lg group-hover:text-primary transition-colors"></i>
              <span class="text-sm font-medium"><%= getCurrentLanguage().code.toUpperCase() %></span>
              <i class="ti ti-chevron-down text-xs group-hover:text-primary transition-all duration-200 group-hover:rotate-180"></i>
            </button>

            <div id="language-dropdown" class="hidden absolute right-0 top-full mt-2 w-52 bg-dark-800/95 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-2xl z-[9999] overflow-hidden">
              <div class="p-2">
                <div class="text-xs font-medium text-gray-500 px-3 py-2 border-b border-gray-700/50 mb-1">
                  <%= t('common.language') %>
                </div>
                <% getLanguages().forEach(function(lang) { %>
                <a href="<%= getLanguageUrl(lang.code) %>"
                   class="group flex items-center px-3 py-2.5 text-sm text-gray-300 hover:bg-dark-700/70 hover:text-white transition-all duration-200 rounded-lg <%= locale === lang.code ? 'bg-primary/20 text-primary border border-primary/30' : '' %>">
                  <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700/50 group-hover:bg-primary/20 transition-colors mr-3 <%= locale === lang.code ? 'bg-primary/30' : '' %>">
                    <span class="text-xs font-bold <%= locale === lang.code ? 'text-primary' : 'text-gray-400 group-hover:text-primary' %>">
                      <%= lang.code.toUpperCase() %>
                    </span>
                  </div>
                  <div class="flex-1">
                    <div class="font-medium"><%= lang.native %></div>
                    <div class="text-xs text-gray-500"><%= lang.name %></div>
                  </div>
                  <% if (locale === lang.code) { %>
                  <i class="ti ti-check text-primary ml-2"></i>
                  <% } %>
                </a>
                <% }); %>
              </div>
            </div>
          </div>

          <a href="/login" class="text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.login') %></a>
          <a href="/register" class="cta-primary"><%= t('landing.nav.get_started') %></a>
        </div>
        
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button id="mobile-menu-btn" class="text-gray-300 hover:text-white">
            <i class="ti ti-menu-2 text-xl"></i>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Mobile menu -->
    <div id="mobile-menu" class="hidden md:hidden bg-dark-800 border-t border-gray-700">
      <div class="px-4 py-4 space-y-4">
        <a href="#how-it-works" class="block text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.how_it_works') %></a>
        <a href="#features" class="block text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.features') %></a>
        <a href="#video-tutorial" class="block text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.video_tutorial') %></a>
        <a href="#pricing" class="block text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.pricing') %></a>
        <a href="#faq" class="block text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.faq') %></a>
        <a href="https://t.me/streamonpod_support" target="_blank" class="block text-gray-300 hover:text-white transition-colors"><%= t('landing.nav.support') %></a>

        <!-- Mobile Language Switcher -->
        <div class="pt-4 border-t border-gray-700">
          <div class="mb-4">
            <div class="text-sm font-medium text-gray-400 mb-2"><%= t('common.language') %></div>
            <div class="space-y-2">
              <% getLanguages().forEach(function(lang) { %>
              <a href="<%= getLanguageUrl(lang.code) %>"
                 class="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-dark-700/70 hover:text-white transition-all duration-200 rounded-lg <%= locale === lang.code ? 'bg-primary/20 text-primary' : '' %>">
                <div class="flex items-center justify-center w-6 h-6 rounded bg-gray-700/50 mr-3 <%= locale === lang.code ? 'bg-primary/30' : '' %>">
                  <span class="text-xs font-bold <%= locale === lang.code ? 'text-primary' : 'text-gray-400' %>">
                    <%= lang.code.toUpperCase() %>
                  </span>
                </div>
                <span class="font-medium"><%= lang.native %></span>
                <% if (locale === lang.code) { %>
                <i class="ti ti-check text-primary ml-auto"></i>
                <% } %>
              </a>
              <% }); %>
            </div>
          </div>

          <a href="/login" class="block text-gray-300 hover:text-white transition-colors mb-2"><%= t('landing.nav.login') %></a>
          <a href="/register" class="cta-primary w-full text-center"><%= t('landing.nav.get_started') %></a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero-gradient min-h-screen flex items-center justify-center relative pt-16">
    <!-- Floating elements -->
    <div class="floating-element">
      <i class="ti ti-broadcast text-6xl text-primary"></i>
    </div>
    <div class="floating-element">
      <i class="ti ti-video text-4xl text-primary"></i>
    </div>
    <div class="floating-element">
      <i class="ti ti-device-tv text-5xl text-primary"></i>
    </div>
    
    <div class="hero-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="fade-in-up">
        <h1 class="hero-title text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
          <%= t('landing.hero.title') %>
        </h1>
        <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
          <%= t('landing.hero.subtitle') %>
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
          <a href="/register" class="cta-primary btn-glow">
            <i class="ti ti-rocket"></i>
            <%= t('landing.hero.cta_primary') %>
          </a>
          <a href="#features" class="cta-secondary btn-glow">
            <i class="ti ti-info-circle"></i>
            <%= t('landing.hero.cta_secondary') %>
          </a>
        </div>

        <!-- Platform Icons -->
        <div class="flex items-center justify-center gap-4 mb-8">
          <span class="text-gray-400 text-sm"><%= t('landing.hero.streaming_to') %></span>
          <div class="flex items-center gap-3">
            <div class="platform-icon platform-youtube">
              <i class="ti ti-brand-youtube text-white text-xl"></i>
            </div>
            <div class="platform-icon platform-facebook">
              <i class="ti ti-brand-facebook text-white text-xl"></i>
            </div>
            <div class="platform-icon platform-twitch">
              <i class="ti ti-brand-twitch text-white text-xl"></i>
            </div>
            <div class="platform-icon platform-tiktok">
              <i class="ti ti-brand-tiktok text-white text-xl"></i>
            </div>
            <div class="platform-icon platform-instagram">
              <i class="ti ti-brand-instagram text-white text-xl"></i>
            </div>
          </div>
        </div>
        
        <!-- Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
          <div class="text-center">
            <div class="stat-number"><%= t('landing.hero.stats.uptime') %></div>
            <div class="text-gray-400"><%= t('landing.hero.stats.uptime_label') %></div>
          </div>
          <div class="text-center">
            <div class="stat-number"><%- t('landing.hero.stats.streaming') %></div>
            <div class="text-gray-400"><%= t('landing.hero.stats.streaming_label') %></div>
          </div>
          <div class="text-center">
            <div class="stat-number"><%= t('landing.hero.stats.platforms') %></div>
            <div class="text-gray-400"><%= t('landing.hero.stats.platforms_label') %></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="section-padding bg-dark-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="fade-in-up">
        <h2 class="section-title"><%= t('landing.features.title') %></h2>
        <p class="section-subtitle">
          <%= t('landing.features.subtitle') %>
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Feature 1 -->
        <div class="feature-card fade-in-up">
          <div class="feature-icon">
            <i class="ti ti-clock text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.features.feature_1.title') %></h3>
          <p class="text-gray-300 leading-relaxed">
            <%= t('landing.features.feature_1.desc') %>
          </p>
        </div>

        <!-- Feature 2 -->
        <div class="feature-card fade-in-up">
          <div class="feature-icon">
            <i class="ti ti-device-tv text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.features.feature_2.title') %></h3>
          <p class="text-gray-300 leading-relaxed">
            <%= t('landing.features.feature_2.desc') %>
          </p>
        </div>

        <!-- Feature 3 -->
        <div class="feature-card fade-in-up">
          <div class="feature-icon">
            <i class="ti ti-calendar text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.features.feature_3.title') %></h3>
          <p class="text-gray-300 leading-relaxed">
            <%= t('landing.features.feature_3.desc') %>
          </p>
        </div>
        
        <!-- Feature 4 -->
        <div class="feature-card fade-in-up">
          <div class="feature-icon">
            <i class="ti ti-layout-dashboard text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.features.feature_4.title') %></h3>
          <p class="text-gray-300 leading-relaxed">
            <%= t('landing.features.feature_4.desc') %>
          </p>
        </div>

        <!-- Feature 5 -->
        <div class="feature-card fade-in-up">
          <div class="feature-icon">
            <i class="ti ti-server text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.features.feature_5.title') %></h3>
          <p class="text-gray-300 leading-relaxed">
            <%= t('landing.features.feature_5.desc') %>
          </p>
        </div>

        <!-- Feature 6 -->
        <div class="feature-card fade-in-up">
          <div class="feature-icon">
            <i class="ti ti-headset text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.features.feature_6.title') %></h3>
          <p class="text-gray-300 leading-relaxed">
            <%= t('landing.features.feature_6.desc') %>
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section id="how-it-works" class="section-padding bg-dark-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="fade-in-up">
        <h2 class="section-title"><%= t('landing.how_it_works.title') %></h2>
        <p class="section-subtitle">
          <%= t('landing.how_it_works.subtitle') %>
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Step 1 -->
        <div class="text-center fade-in-up">
          <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">1</span>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.how_it_works.step_1.title') %></h3>
          <p class="text-gray-300">
            <%= t('landing.how_it_works.step_1.desc') %>
          </p>
        </div>

        <!-- Step 2 -->
        <div class="text-center fade-in-up">
          <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">2</span>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.how_it_works.step_2.title') %></h3>
          <p class="text-gray-300">
            <%= t('landing.how_it_works.step_2.desc') %>
          </p>
        </div>

        <!-- Step 3 -->
        <div class="text-center fade-in-up">
          <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl font-bold text-white">3</span>
          </div>
          <h3 class="text-xl font-semibold mb-3"><%= t('landing.how_it_works.step_3.title') %></h3>
          <p class="text-gray-300">
            <%= t('landing.how_it_works.step_3.desc') %>
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Video Tutorial Section -->
  <section id="video-tutorial" class="section-padding bg-dark-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="fade-in-up">
        <h2 class="section-title"><%= t('landing.video_tutorial.title') %></h2>
        <p class="section-subtitle">
          <%= t('landing.video_tutorial.subtitle') %>
        </p>
      </div>

      <div class="max-w-4xl mx-auto">
        <div class="video-container fade-in-up">
          <div class="relative bg-dark-700 rounded-2xl overflow-hidden shadow-2xl">
            <!-- Video Iframe -->
            <div class="aspect-video">
              <iframe
                src="https://drive.google.com/file/d/1cKZ0Fv9uijxGv4RZdZf24iu5zzjKFwr3/preview"
                class="w-full h-full border-0"
                allow="autoplay"
                loading="lazy"
                title="<%= t('landing.video_tutorial.video_title') %>">
              </iframe>
            </div>

            <!-- Video Overlay for Loading -->
            <div id="video-loading" class="absolute inset-0 bg-dark-700 flex items-center justify-center opacity-100 transition-opacity duration-500">
              <div class="text-center">
                <div class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p class="text-gray-400"><%= t('landing.video_tutorial.loading') %></p>
              </div>
            </div>
          </div>

          <!-- Video Description -->
          <div class="mt-8 text-center">
            <h3 class="text-xl font-semibold mb-4"><%= t('landing.video_tutorial.video_title') %></h3>
            <p class="text-gray-300 leading-relaxed max-w-2xl mx-auto">
              <%= t('landing.video_tutorial.description') %>
            </p>

            <!-- Video Stats -->
            <div class="flex items-center justify-center gap-8 mt-6 text-sm text-gray-400">
              <div class="flex items-center gap-2">
                <i class="ti ti-clock"></i>
                <span><%= t('landing.video_tutorial.duration') %></span>
              </div>
              <div class="flex items-center gap-2">
                <i class="ti ti-eye"></i>
                <span><%= t('landing.video_tutorial.views') %></span>
              </div>
              <div class="flex items-center gap-2">
                <i class="ti ti-language"></i>
                <span><%= t('landing.video_tutorial.language') %></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section id="pricing" class="pricing-section section-padding bg-dark-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="fade-in-up">
        <h2 class="section-title"><%= t('landing.pricing.title') %></h2>
        <p class="section-subtitle">
          <%= t('landing.pricing.subtitle') %>
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto pt-16">
        <% if (plans && plans.length > 0) { %>
          <% plans.forEach((plan, index) => { %>
            <% const isPopular = plan.name === 'PodFlow'; // Mark PodFlow as popular %>
            <% const isPreview = plan.name === 'Preview'; %>

            <!-- <%= plan.name %> Plan -->
            <div class="feature-card <%= isPopular ? 'pricing-card-popular' : '' %> text-center relative">
              <% if (isPopular) { %>
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2 z-50" style="margin-top: -12px;">
                  <div class="popular-badge">
                    ⭐ Populer
                  </div>
                </div>
              <% } %>

              <div class="mb-6 <%= isPopular ? 'pt-12' : 'pt-4' %>">
                <h3 class="text-2xl font-bold mb-2"><%= plan.name %></h3>
                <div class="text-4xl font-bold text-primary mb-2">
                  <% if (plan.price === 0) { %>
                    Free
                  <% } else { %>
                    <%= plan.currency === 'IDR' ? 'Rp' : '$' %><%= plan.price.toLocaleString() %>
                  <% } %>
                </div>
                <p class="text-gray-400">
                  <% if (plan.price === 0) { %>
                    Coba gratis tanpa biaya
                  <% } else { %>
                    Per <%= plan.billing_period === 'monthly' ? 'bulan' : plan.billing_period %>
                  <% } %>
                </p>
              </div>

              <ul class="space-y-3 mb-8 text-left">
                <li class="flex items-center">
                  <i class="ti ti-check text-green-400 mr-3"></i>
                  <span><%= plan.max_streaming_slots %> Streaming Slots</span>
                </li>
                <li class="flex items-center">
                  <i class="ti ti-check text-green-400 mr-3"></i>
                  <span>
                    <%
                      // Format storage display
                      let storageDisplay = '';
                      const storageGB = plan.max_storage_gb;

                      if (storageGB < 1) {
                        // Convert to MB if less than 1 GB
                        const storageMB = Math.round(storageGB * 1024);
                        storageDisplay = storageMB + ' MB Storage';
                      } else if (storageGB === Math.floor(storageGB)) {
                        // Show as integer GB if it's a whole number
                        storageDisplay = Math.floor(storageGB) + ' GB Storage';
                      } else {
                        // Show with 1 decimal place for fractional GB
                        storageDisplay = storageGB.toFixed(1) + ' GB Storage';
                      }
                    %>
                    <%= storageDisplay %>
                  </span>
                </li>
                <% if (plan.features && plan.features.length > 0) { %>
                  <% plan.features.forEach(feature => { %>
                    <li class="flex items-center">
                      <i class="ti ti-check text-green-400 mr-3"></i>
                      <span><%= feature %></span>
                    </li>
                  <% }); %>
                <% } else { %>
                  <!-- Default features if none specified -->
                  <% if (isPreview) { %>
                    <li class="flex items-center">
                      <i class="ti ti-check text-green-400 mr-3"></i>
                      <span>Basic Support</span>
                    </li>
                  <% } else { %>
                    <li class="flex items-center">
                      <i class="ti ti-check text-green-400 mr-3"></i>
                      <span>Basic Support</span>
                    </li>
                  <% } %>
                <% } %>
              </ul>

              <a href="/register" class="<%= isPopular ? 'cta-primary' : 'cta-secondary' %> w-full">
                <% if (isPreview) { %>
                  Mulai Gratis
                <% } else { %>
                  Pilih <%= plan.name %>
                <% } %>
              </a>
            </div>
          <% }); %>
        <% } else { %>
          <!-- Fallback static content if no plans loaded -->
          <div class="feature-card text-center">
            <div class="mb-6 pt-4">
              <h3 class="text-2xl font-bold mb-2">Preview</h3>
              <div class="text-4xl font-bold text-primary mb-2">Free</div>
              <p class="text-gray-400">Coba gratis tanpa biaya</p>
            </div>
            <ul class="space-y-3 mb-8 text-left">
              <li class="flex items-center">
                <i class="ti ti-check text-green-400 mr-3"></i>
                <span>0 Streaming Slots</span>
              </li>
              <li class="flex items-center">
                <i class="ti ti-check text-green-400 mr-3"></i>
                <span>100 MB Storage</span>
              </li>
              <li class="flex items-center">
                <i class="ti ti-check text-green-400 mr-3"></i>
                <span>Dashboard Access</span>
              </li>
            </ul>
            <a href="/register" class="cta-secondary w-full">Mulai Gratis</a>
          </div>

          <div class="feature-card pricing-card-popular text-center relative">
            <div class="absolute -top-3 left-1/2 transform -translate-x-1/2 z-50" style="margin-top: -12px;">
              <div class="popular-badge">⭐ Populer</div>
            </div>
            <div class="mb-6 pt-12">
              <h3 class="text-2xl font-bold mb-2">Basic</h3>
              <div class="text-4xl font-bold text-primary mb-2">$10</div>
              <p class="text-gray-400">Per bulan</p>
            </div>
            <ul class="space-y-3 mb-8 text-left">
              <li class="flex items-center">
                <i class="ti ti-check text-green-400 mr-3"></i>
                <span>1 Streaming Slot</span>
              </li>
              <li class="flex items-center">
                <i class="ti ti-check text-green-400 mr-3"></i>
                <span>100 MB Storage</span>
              </li>
              <li class="flex items-center">
                <i class="ti ti-check text-green-400 mr-3"></i>
                <span>Basic Support</span>
              </li>
            </ul>
            <a href="/register" class="cta-primary w-full">Pilih Basic</a>
          </div>
        <% } %>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section id="faq" class="section-padding bg-dark-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="fade-in-up">
        <h2 class="section-title"><%= t('landing.faq.title') %></h2>
        <p class="section-subtitle">
          <%= t('landing.faq.subtitle') %>
        </p>
      </div>

      <div class="space-y-4">
        <!-- FAQ Item 1 -->
        <div class="faq-item">
          <div class="faq-question" onclick="toggleFaq(this)">
            <h3 class="font-semibold"><%= t('landing.faq.q1.question') %></h3>
            <i class="ti ti-chevron-down faq-icon text-primary"></i>
          </div>
          <div class="faq-answer">
            <%= t('landing.faq.q1.answer') %>
          </div>
        </div>

        <!-- FAQ Item 2 -->
        <div class="faq-item">
          <div class="faq-question" onclick="toggleFaq(this)">
            <h3 class="font-semibold"><%= t('landing.faq.q2.question') %></h3>
            <i class="ti ti-chevron-down faq-icon text-primary"></i>
          </div>
          <div class="faq-answer">
            <%= t('landing.faq.q2.answer') %>
          </div>
        </div>

        <!-- FAQ Item 3 -->
        <div class="faq-item">
          <div class="faq-question" onclick="toggleFaq(this)">
            <h3 class="font-semibold"><%= t('landing.faq.q3.question') %></h3>
            <i class="ti ti-chevron-down faq-icon text-primary"></i>
          </div>
          <div class="faq-answer">
            <%= t('landing.faq.q3.answer') %>
          </div>
        </div>

        <!-- FAQ Item 4 -->
        <div class="faq-item">
          <div class="faq-question" onclick="toggleFaq(this)">
            <h3 class="font-semibold"><%= t('landing.faq.q4.question') %></h3>
            <i class="ti ti-chevron-down faq-icon text-primary"></i>
          </div>
          <div class="faq-answer">
            <%= t('landing.faq.q4.answer') %>
          </div>
        </div>

        <!-- FAQ Item 5 -->
        <div class="faq-item">
          <div class="faq-question" onclick="toggleFaq(this)">
            <h3 class="font-semibold"><%= t('landing.faq.q5.question') %></h3>
            <i class="ti ti-chevron-down faq-icon text-primary"></i>
          </div>
          <div class="faq-answer">
            <%= t('landing.faq.q5.answer') %>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="section-padding bg-dark-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="fade-in-up">
        <h2 class="section-title"><%= t('landing.testimonials.title') %></h2>
        <p class="section-subtitle">
          <%= t('landing.testimonials.subtitle') %>
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Testimonial 1 -->
        <div class="testimonial-card fade-in-up">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold">D</span>
            </div>
            <div>
              <h4 class="font-semibold"><%= t('landing.testimonials.testimonial_1.name') %></h4>
              <p class="text-gray-400 text-sm"><%= t('landing.testimonials.testimonial_1.role') %></p>
            </div>
          </div>
          <p class="text-gray-300 leading-relaxed">
            "<%= t('landing.testimonials.testimonial_1.text') %>"
          </p>
        </div>

        <!-- Testimonial 2 -->
        <div class="testimonial-card fade-in-up">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold">L</span>
            </div>
            <div>
              <h4 class="font-semibold"><%= t('landing.testimonials.testimonial_2.name') %></h4>
              <p class="text-gray-400 text-sm"><%= t('landing.testimonials.testimonial_2.role') %></p>
            </div>
          </div>
          <p class="text-gray-300 leading-relaxed">
            "<%= t('landing.testimonials.testimonial_2.text') %>"
          </p>
        </div>

        <!-- Testimonial 3 -->
        <div class="testimonial-card fade-in-up">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold">F</span>
            </div>
            <div>
              <h4 class="font-semibold"><%= t('landing.testimonials.testimonial_3.name') %></h4>
              <p class="text-gray-400 text-sm"><%= t('landing.testimonials.testimonial_3.role') %></p>
            </div>
          </div>
          <p class="text-gray-300 leading-relaxed">
            "<%= t('landing.testimonials.testimonial_3.text') %>"
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="section-padding bg-dark-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="fade-in-up">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">
          <%= t('landing.cta.title') %>
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          <%= t('landing.cta.subtitle') %>
        </p>

        <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
          <a href="/register" class="cta-primary text-lg px-8 py-4">
            <i class="ti ti-rocket"></i>
            <%= t('landing.cta.start_streaming') %>
          </a>
          <a href="https://t.me/streamonpod_support" target="_blank" class="cta-secondary text-lg px-8 py-4">
            <i class="ti ti-brand-telegram"></i>
            <%= t('landing.cta.contact_support') %>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-dark-800 border-t border-gray-700 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Logo & Description -->
        <div class="md:col-span-2">
          <img src="/images/streamonpod-logotype.png" alt="StreamOnPod" class="h-16 mb-4">
          <p class="text-gray-400 leading-relaxed mb-4">
            <%= t('landing.footer.description') %>
          </p>
          <div class="flex items-center space-x-4">
            <a href="https://t.me/streamonpod_support" target="_blank" class="text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-brand-telegram text-xl"></i>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="font-semibold mb-4"><%= t('landing.footer.quick_links') %></h3>
          <ul class="space-y-2">
            <li><a href="#how-it-works" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.nav.how_it_works') %></a></li>
            <li><a href="#features" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.nav.features') %></a></li>
            <li><a href="#video-tutorial" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.nav.video_tutorial') %></a></li>
            <li><a href="#pricing" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.nav.pricing') %></a></li>
            <li><a href="#faq" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.nav.faq') %></a></li>
            <li><a href="/login" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.nav.login') %></a></li>
          </ul>
        </div>

        <!-- Support -->
        <div>
          <h3 class="font-semibold mb-4"><%= t('landing.footer.support_title') %></h3>
          <ul class="space-y-2">
            <li><a href="https://t.me/streamonpod_support" target="_blank" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.footer.help_center') %></a></li>
            <li><a href="https://t.me/streamonpod_support" target="_blank" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.footer.contact_us') %></a></li>
            <li><a href="/tos" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.footer.terms_of_service') %></a></li>
            <li><a href="/privacy-policy" class="text-gray-400 hover:text-white transition-colors"><%= t('landing.footer.privacy_policy') %></a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-700 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2025 StreamOnPod. <%= t('landing.footer.copyright') %>
        </p>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="/js/landing.js"></script>

  <!-- Language Switcher Script -->
  <script>
  // Language switcher functionality
  document.addEventListener('DOMContentLoaded', () => {
    const languageBtn = document.getElementById('language-btn');
    const languageDropdown = document.getElementById('language-dropdown');

    if (languageBtn && languageDropdown) {
      languageBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        languageDropdown.classList.toggle('hidden');

        // Add animation
        if (!languageDropdown.classList.contains('hidden')) {
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px)';
          setTimeout(() => {
            languageDropdown.style.opacity = '1';
            languageDropdown.style.transform = 'translateY(0)';
          }, 10);
        }
      });

      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = languageDropdown.contains(e.target);
        const isClickOnButton = languageBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !languageDropdown.classList.contains('hidden')) {
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px)';
          setTimeout(() => {
            languageDropdown.classList.add('hidden');
          }, 150);
        }
      });

      languageDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }
  });
  </script>

  <style>
  #language-dropdown {
    transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  }
  </style>
</body>
</html>
