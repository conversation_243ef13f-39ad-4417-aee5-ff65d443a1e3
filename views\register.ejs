<!DOCTYPE html>
<html lang="<%= locale %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= t('auth.register_title') %> - StreamOnPod</title>
  <link rel="icon" href="/images/streamonpod-logo.png" type="image/png">
  <link rel="preload" href="/images/streamonpod-logotype.png" as="image" type="image/png">
  <link rel="alternate icon" href="/images/favicon.ico" type="image/x-icon">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">
  <link rel="stylesheet" href="/css/styles.css">
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'inter': ['Inter', 'sans-serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif']
        },
        extend: {
          colors: {
            'primary': '#ad6610',
            'secondary': '#8b5209',
            'dark': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '100': '#E5E5E5',
            }
          }
        }
      }
    }
  </script>
</head>
<body class="bg-dark-900 text-white font-inter">
  <%- include('partials/language-switcher') %>

  <div class="min-h-screen flex items-center justify-center px-6">
    <div class="w-full max-w-md card-enhanced p-8">

      <div class="flex justify-center mb-8">
        <img src="/images/streamonpod-logotype.png" alt="StreamOnPod Logo" class="h-32">
      </div>

      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-white mb-2"><%= t('auth.register_title') %></h1>
        <p class="text-gray-400"><%= t('auth.register_subtitle') %></p>
      </div>

      <% if (error) { %>
        <div id="error-message" class="bg-red-500/10 border border-red-500/20 text-red-400 px-4 py-3 rounded-lg mb-6">
          <div class="flex items-center">
            <i class="ti ti-alert-circle mr-2"></i>
            <span>
              <%= error %>
            </span>
          </div>
        </div>
      <% } %>

      <form action="/register" method="POST" class="space-y-5">
        <input type="hidden" name="_csrf" value="<%= csrfToken %>">

        <!-- Username -->
        <div>
          <label for="username" class="text-sm font-medium block mb-2"><%= t('auth.username') %></label>
          <div class="relative">
            <input type="text" id="username" name="username" required
                   value="<%= typeof user !== 'undefined' ? user.username || '' : '' %>"
                   class="w-full pl-10 pr-4 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                   placeholder="<%= t('auth.username') %>">
            <i class="ti ti-user absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        <!-- Email -->
        <div>
          <label for="email" class="text-sm font-medium block mb-2"><%= t('auth.email') %></label>
          <div class="relative">
            <input type="email" id="email" name="email"
                   value="<%= typeof user !== 'undefined' ? user.email || '' : '' %>"
                   class="w-full pl-10 pr-4 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                   placeholder="<%= t('auth.email') %>">
            <i class="ti ti-mail absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        <!-- Password -->
        <div>
          <label for="password" class="text-sm font-medium block mb-2"><%= t('auth.password') %></label>
          <div class="relative">
            <input type="password" id="password" name="password" required
                   class="w-full pl-10 pr-12 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                   placeholder="<%= t('auth.password') %>">
            <i class="ti ti-lock absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
            <button type="button" id="toggle-password" class="absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-eye text-base" id="password-icon"></i>
            </button>
          </div>
          <p class="text-xs text-gray-400 mt-1">At least 8 characters with uppercase, lowercase, and number</p>
        </div>

        <!-- Confirm Password -->
        <div class="pb-3">
          <label for="confirmPassword" class="text-sm font-medium block mb-2"><%= t('auth.confirm_password') %></label>
          <div class="relative">
            <input type="password" id="confirmPassword" name="confirmPassword" required
                   class="w-full pl-10 pr-12 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                   placeholder="<%= t('auth.confirm_password') %>">
            <i class="ti ti-lock absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
            <button type="button" id="toggle-confirm-password" class="absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-eye text-base" id="confirm-password-icon"></i>
            </button>
          </div>
        </div>

        <!-- Submit Button -->
        <button type="submit" class="btn-primary-enhanced w-full py-2.5 font-medium">
          <%= t('auth.register_button') %>
        </button>
      </form>

      <!-- Login Link -->
      <div class="text-center mt-6">
        <p class="text-gray-400">
          <%= t('auth.already_have_account') %>
          <a href="/login" class="text-primary hover:underline"><%= t('auth.sign_in') %></a>
        </p>
      </div>
    </div>
  </div>

  <script>
    // Password visibility toggle functions
    function togglePasswordVisibility() {
      const password = document.getElementById('password');
      const passwordToggle = document.getElementById('password-icon');
      if (password.type === 'password') {
        password.type = 'text';
        passwordToggle.classList.remove('ti-eye');
        passwordToggle.classList.add('ti-eye-off');
      } else {
        password.type = 'password';
        passwordToggle.classList.remove('ti-eye-off');
        passwordToggle.classList.add('ti-eye');
      }
    }

    function toggleConfirmPasswordVisibility() {
      const confirmPassword = document.getElementById('confirmPassword');
      const confirmPasswordToggle = document.getElementById('confirm-password-icon');
      if (confirmPassword.type === 'password') {
        confirmPassword.type = 'text';
        confirmPasswordToggle.classList.remove('ti-eye');
        confirmPasswordToggle.classList.add('ti-eye-off');
      } else {
        confirmPassword.type = 'password';
        confirmPasswordToggle.classList.remove('ti-eye-off');
        confirmPasswordToggle.classList.add('ti-eye');
      }
    }

    // Event listeners
    document.getElementById('toggle-password').addEventListener('click', togglePasswordVisibility);
    document.getElementById('toggle-confirm-password').addEventListener('click', toggleConfirmPasswordVisibility);

    // Password strength validation
    document.getElementById('password').addEventListener('input', function() {
      const password = this.value;
      const hasLower = /[a-z]/.test(password);
      const hasUpper = /[A-Z]/.test(password);
      const hasNumber = /[0-9]/.test(password);
      const hasLength = password.length >= 8;
      
      if (hasLower && hasUpper && hasNumber && hasLength) {
        this.classList.remove('border-red-500');
        this.classList.add('border-green-500');
      } else {
        this.classList.remove('border-green-500');
        this.classList.add('border-red-500');
      }
    });

    // Confirm password validation
    document.getElementById('confirmPassword').addEventListener('input', function() {
      const password = document.getElementById('password').value;
      const confirmPassword = this.value;
      
      if (password === confirmPassword && confirmPassword !== '') {
        this.classList.remove('border-red-500');
        this.classList.add('border-green-500');
      } else {
        this.classList.remove('border-green-500');
        this.classList.add('border-red-500');
      }
    });
  </script>
</body>

</html>
