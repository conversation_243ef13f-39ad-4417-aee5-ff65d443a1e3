/*
 * StreamOnPod v2.0 - Advanced Cloud Streaming Platform
 *
 * Modern UI Components & Enhanced Features
 * Support: https://t.me/streamonpod_support

 * © 2025 StreamOnPod Team - All rights reserved
 */

.sidebar-icon {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 4rem;
  width: 4rem;
  margin: 0 auto;
  margin-bottom: 0.75rem;
  color: white;
  border-radius: 0.75rem;
  transition-property: all;
  transition-timing-function: linear;
  transition-duration: 300ms;
  cursor: pointer;
}

.sidebar-icon:hover {
  background-color: var(--primary-color);
  color: white;
  border-radius: 0.75rem;
}

.sidebar-tooltip {
  position: absolute;
  width: auto;
  padding: 0.5rem;
  margin: 0.5rem;
  min-width: max-content;
  left: 5rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  color: white;
  background-color: #121212;
  font-size: 0.75rem;
  font-weight: bold;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 100ms;
  transform: scale(0);
  transform-origin: left;
}

.sidebar-icon:hover .sidebar-tooltip {
  transform: scale(1);
}

/* Header Navigation Styles */
.h-20 {
  height: 5rem;
}

.header-nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  color: #9CA3AF;
  border-radius: 1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 600;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
}

.header-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.6s ease;
}

.header-nav-item:hover::before {
  left: 100%;
}

.header-nav-item:hover {
  background-color: rgba(173, 102, 16, 0.2);
  color: white;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(173, 102, 16, 0.3);
}

.header-nav-item i {
  font-size: 1.25rem;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.header-nav-item:hover i {
  transform: scale(1.1);
}

.header-nav-active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: 0 6px 20px rgba(173, 102, 16, 0.4);
  transform: translateY(-1px);
  position: relative;
}

.header-nav-active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 4px;
  background: linear-gradient(90deg, transparent, #ffffff, transparent);
  border-radius: 2px;
  animation: activeGlow 2s ease-in-out infinite alternate;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

@keyframes activeGlow {
  0% {
    opacity: 0.7;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  }
  100% {
    opacity: 1;
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.7);
  }
}

.header-nav-active:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 30px rgba(173, 102, 16, 0.5);
}

/* Desktop header adjustments */
.lg\:pt-24 {
  padding-top: 5rem !important;
}

/* Responsive adjustments for header navigation */
@media (max-width: 1400px) {
  .header-nav-item {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 1200px) {
  .header-nav-item span {
    display: none;
  }

  .header-nav-item {
    padding: 0.75rem;
    min-width: 3rem;
    justify-content: center;
  }
}

@media (max-width: 1024px) {
  .header-nav-item {
    padding: 0.5rem;
    min-width: 2.5rem;
  }
}

/* Profile button styling */
#desktop-profile-btn {
  border-radius: 1rem;
  padding: 0.75rem 1.25rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

#desktop-profile-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.6s ease;
}

#desktop-profile-btn:hover::before {
  left: 100%;
}

#desktop-profile-btn:hover {
  background-color: rgba(173, 102, 16, 0.15);
  border-color: rgba(173, 102, 16, 0.3);
  color: white;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(173, 102, 16, 0.2);
}

#desktop-profile-btn .w-6 {
  transition: transform 0.3s ease;
}

#desktop-profile-btn:hover .w-6 {
  transform: scale(1.1);
}

/* Dropdown positioning fix */
#desktop-profile-dropdown {
  min-width: 200px;
  margin-top: 0.5rem;
}

/* Enhanced Header improvements */
.header-logo {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(1);
}

.header-logo:hover {
  transform: scale(1.05) rotate(1deg);
  filter: brightness(1.1);
  cursor: pointer;
}

/* Header spacing improvements */
.header-nav-item {
  margin: 0 0.25rem;
}

.header-action-btn {
  margin: 0 0.125rem;
}

/* Enhanced button feedback */
.header-action-btn:focus {
  outline: 2px solid rgba(173, 102, 16, 0.5);
  outline-offset: 2px;
}

.header-nav-item:focus {
  outline: 2px solid rgba(173, 102, 16, 0.5);
  outline-offset: 2px;
}

/* Loading state for buttons */
.header-action-btn.loading {
  pointer-events: none;
  opacity: 0.7;
}

.header-action-btn.loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Action buttons styling */
.header-action-btn {
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #9CA3AF;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  background: rgba(45, 45, 45, 0.3);
  backdrop-filter: blur(10px);
}

.header-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(173, 102, 16, 0.3), transparent);
  transition: left 0.5s ease;
  z-index: 0;
}

.header-action-btn:hover::before {
  left: 100%;
}

.header-action-btn:hover {
  background: linear-gradient(135deg, rgba(173, 102, 16, 0.2), rgba(209, 127, 20, 0.2));
  border-color: rgba(173, 102, 16, 0.5);
  color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(173, 102, 16, 0.3), 0 0 20px rgba(173, 102, 16, 0.1);
}

.header-action-btn:active {
  transform: translateY(-1px) scale(1.02);
}

.header-action-btn i {
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.header-action-btn:hover i {
  transform: scale(1.15) rotate(5deg);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(173, 102, 16, 0.5);
}

.header-action-btn span {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.header-action-btn:hover span {
  color: white;
  text-shadow: 0 0 5px rgba(173, 102, 16, 0.3);
}

/* Enhanced dropdown animations */
#language-dropdown.show {
  opacity: 1 !important;
  transform: translateY(0) scale(1) !important;
  visibility: visible;
}

#desktop-profile-dropdown.show {
  opacity: 1 !important;
  transform: translateY(0) scale(1) !important;
  visibility: visible;
}

/* Page Indicator Styling */
.page-indicator {
  animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Profile Dropdown Animations */
#desktop-profile-dropdown {
  transform-origin: top right;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  visibility: hidden;
}

#desktop-profile-dropdown.show {
  opacity: 1;
  transform: translateY(0) scale(1);
  visibility: visible;
}

/* Language Dropdown Enhanced Animations */
#language-dropdown {
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top right;
}

#language-dropdown.show {
  opacity: 1 !important;
  transform: translateY(0) scale(1) !important;
  visibility: visible;
}

/* Smooth dropdown item animations */
.header-action-btn + div a,
.header-action-btn + div button {
  transition: all 0.2s ease;
  transform: translateX(0);
}

.header-action-btn + div a:hover,
.header-action-btn + div button:hover {
  transform: translateX(4px);
  background-color: rgba(173, 102, 16, 0.1);
}

/* Profile Chevron Animation */
#profile-chevron {
  transition: transform 0.3s ease;
}

#desktop-profile-btn[aria-expanded="true"] #profile-chevron {
  transform: rotate(180deg);
}

/* Ensure proper spacing and alignment */
.header-container {
  max-width: 100%;
  margin: 0 auto;
}

/* History Card Layout Styles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Card Enhanced Hover Effects */
.card-enhanced {
  background: linear-gradient(135deg, #252525 0%, #2D2D2D 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(173, 102, 16, 0.05) 0%, rgba(209, 127, 20, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.card-enhanced:hover::before {
  opacity: 1;
}

.card-enhanced:hover {
  border-color: rgba(173, 102, 16, 0.3);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.2),
    0 4px 6px -4px rgba(0, 0, 0, 0.1),
    0 0 20px rgba(173, 102, 16, 0.1);
  transform: translateY(-2px);
}

/* History Card Specific Styles */
.history-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.history-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.2),
    0 8px 10px -6px rgba(0, 0, 0, 0.1),
    0 0 30px rgba(173, 102, 16, 0.15);
}

/* Platform Badge Styles */
.platform-badge {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Duration Badge Styles */
.duration-badge {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-family: 'JetBrains Mono', 'Courier New', monospace;
}

/* Thumbnail Hover Effects */
.thumbnail-container {
  position: relative;
  overflow: hidden;
}

.thumbnail-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.card-enhanced:hover .thumbnail-container::after {
  transform: translateX(100%);
}

/* Enhanced Fallback Thumbnail Styles */
.fallback-thumbnail {
  background: linear-gradient(135deg, rgba(173, 102, 16, 0.2) 0%, rgba(45, 45, 45, 1) 50%, rgba(209, 127, 20, 0.2) 100%);
  position: relative;
  overflow: hidden;
}

.fallback-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Decorative Pattern Animation */
.decorative-dots {
  animation: pulse-dots 2s ease-in-out infinite alternate;
}

@keyframes pulse-dots {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Enhanced Video Icon Styling */
.video-icon-enhanced {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  box-shadow:
    0 4px 8px rgba(173, 102, 16, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
}

.video-icon-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: inherit;
}

/* Mobile Thumbnail Enhancements */
.mobile-thumbnail-enhanced {
  transition: all 0.3s ease;
}

.mobile-thumbnail-enhanced:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(173, 102, 16, 0.2);
}

/* Gradient Text Effect */
.gradient-text-enhanced {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

:root {
  --primary-color: #ad6610;
  --primary-hover: #8b5209;
  --primary-light: #d17f14;
  --secondary-color: #d17f14;
  --accent-color: #FF6B35;
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --error-color: #EF4444;
  --info-color: #ad6610;
  --dark-900: #121212;
  --dark-800: #252525;
  --dark-700: #2D2D2D;
  --dark-600: #3D3D3D;
  --dark-500: #6E6E6E;
  --dark-400: #8F8F8F;
  --dark-300: #AFAFAF;
  --dark-200: #CFCFCF;
  --dark-100: #E5E5E5;
  --gradient-primary: linear-gradient(135deg, #ad6610 0%, #d17f14 100%);
  --gradient-secondary: linear-gradient(135deg, #d17f14 0%, #ad6610 100%);
  --gradient-accent: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
  --gradient-dark: linear-gradient(135deg, #252525 0%, #121212 100%);
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
}

body {
  font-family: 'Inter', sans-serif;
}

/* Modal Styles - Tailwind Compatible */
#newStreamModal.show,
#editStreamModal.show {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
}

#newStreamModal.show .modal-container,
#editStreamModal.show .modal-container {
  transform: scale(1) !important;
}

/* Override Tailwind hidden class for modals */
#newStreamModal.hidden,
#editStreamModal.hidden {
  display: none !important;
}

/* Modal animation */
#newStreamModal,
#editStreamModal {
  transition: all 0.3s ease;
}

#newStreamModal .modal-container,
#editStreamModal .modal-container {
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

[onclick^="setVideoOrientation"] {
  position: relative;
  overflow: hidden;
}

[onclick^="setVideoOrientation"]:before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--primary-color);
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

[onclick^="setVideoOrientation"].bg-primary:before {
  opacity: 0.1;
}

[onclick^="setVideoOrientation"]:hover:before {
  opacity: 0.05;
}

[onclick^="setVideoOrientation"] i {
  transition: transform 0.2s ease-in-out;
}

[onclick^="setVideoOrientation"]:active i {
  transform: scale(0.9);
}

@media (max-width: 768px) {
  .modal-container {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }

  .modal-form-group {
    margin-bottom: 1rem;
  }

  .modal-input {
    padding: 0.75rem;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Ensure video selector dropdowns appear above video previews */
  #videoSelectorDropdown,
  #editVideoSelectorDropdown {
    z-index: 70 !important;
    position: absolute !important;
  }
}

/* Platform Dropdown Fix */
#platformDropdown, #editPlatformDropdown {
  pointer-events: auto !important;
  z-index: 9999 !important;
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  margin-top: 4px !important;
  width: 192px !important;
  background-color: rgb(31, 41, 55) !important; /* dark-800 - darker background */
  border: 1px solid rgb(55, 65, 81) !important; /* dark-700 border */
  border-radius: 8px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;
}

#platformDropdown.hidden, #editPlatformDropdown.hidden {
  pointer-events: none !important;
  display: none !important;
}

#platformSelector, #editPlatformSelector {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Ensure parent containers don't clip dropdown */
.modal-container, .space-y-4, .space-y-3, .relative {
  overflow: visible !important;
}

/* Platform dropdown options styling */
#platformDropdown .platform-option, #editPlatformDropdown .platform-option {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  padding: 8px 16px !important;
  color: rgb(255, 255, 255) !important; /* white text */
  background: transparent !important;
  border: none !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  font-size: 14px !important;
  text-align: left !important;
}

#platformDropdown .platform-option:hover, #editPlatformDropdown .platform-option:hover {
  background-color: rgb(55, 65, 81) !important; /* dark-700 on hover - darker */
}

#platformDropdown .platform-option i, #editPlatformDropdown .platform-option i {
  margin-right: 8px !important;
  font-size: 16px !important;
}

#platformDropdown .platform-option span, #editPlatformDropdown .platform-option span {
  color: rgb(255, 255, 255) !important;
  font-size: 14px !important;
}

/* Enhanced UI Components */
.card-enhanced {
  background: var(--gradient-dark);
  border: 1px solid var(--dark-600);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.card-enhanced:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-2xl), 0 0 30px rgba(173, 102, 16, 0.15);
  border-color: var(--primary-color);
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-enhanced:hover::before {
  opacity: 1;
}

.card-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(173, 102, 16, 0.1), transparent);
  transition: left 0.8s ease;
}

.card-enhanced:hover::after {
  left: 100%;
}

.btn-primary-enhanced {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.875rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  text-transform: uppercase;
  box-shadow: var(--shadow-md), 0 0 20px rgba(173, 102, 16, 0.3);
}

.btn-primary-enhanced:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-xl), 0 0 30px rgba(173, 102, 16, 0.5);
  background: var(--gradient-secondary);
}

.btn-primary-enhanced:active {
  transform: translateY(0) scale(1.02);
  transition: all 0.1s ease;
}

.btn-primary-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.6s ease;
}

.btn-primary-enhanced:hover::before {
  left: 100%;
}

.btn-secondary-enhanced {
  background: rgba(var(--dark-700), 0.8);
  border: 2px solid var(--dark-600);
  border-radius: var(--border-radius-md);
  padding: 0.875rem 2rem;
  color: var(--dark-100);
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  text-transform: uppercase;
  backdrop-filter: blur(10px);
}

.btn-secondary-enhanced:hover {
  transform: translateY(-2px);
  border-color: var(--primary-color);
  color: white;
  background: rgba(173, 102, 16, 0.1);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(173, 102, 16, 0.2);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-live {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-offline {
  background: rgba(107, 114, 128, 0.1);
  color: #9CA3AF;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.status-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-scheduled {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.glass-effect {
  background: rgba(37, 37, 37, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-shimmer {
  background: linear-gradient(90deg, var(--dark-700) 25%, var(--dark-600) 50%, var(--dark-700) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

/* Upload progress enhancements */
.upload-progress-container {
  position: relative;
  overflow: hidden;
}

.upload-progress-bar {
  position: relative;
  overflow: hidden;
}

.upload-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Enhanced Form Styling */
.form-enhanced {
  background: var(--gradient-dark);
  border: 1px solid var(--dark-600);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.form-group-enhanced {
  margin-bottom: 1.5rem;
}

.form-label-enhanced {
  display: block;
  color: var(--dark-100);
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  letter-spacing: 0.025em;
}

.form-input-enhanced {
  width: 100%;
  background: rgba(15, 23, 42, 0.8);
  border: 2px solid var(--dark-600);
  border-radius: var(--border-radius-md);
  padding: 0.875rem 1rem;
  color: white;
  font-size: 0.875rem;
  line-height: 1.4;
  height: auto;
  min-height: 48px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5px);
  box-sizing: border-box;
  vertical-align: middle;
}

.form-input-enhanced:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(15, 23, 42, 0.9);
  box-shadow: 0 0 0 4px rgba(173, 102, 16, 0.1), var(--shadow-md);
  transform: translateY(-1px);
}

.form-input-enhanced::placeholder {
  color: var(--dark-400);
}

.form-select-enhanced {
  width: 100%;
  background: rgba(15, 23, 42, 0.8);
  border: 2px solid var(--dark-600);
  border-radius: var(--border-radius-md);
  padding: 0.875rem 1rem;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5px);
  cursor: pointer;
}

.form-select-enhanced:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(15, 23, 42, 0.9);
  box-shadow: 0 0 0 4px rgba(173, 102, 16, 0.1), var(--shadow-md);
}

.form-textarea-enhanced {
  width: 100%;
  background: rgba(15, 23, 42, 0.8);
  border: 2px solid var(--dark-600);
  border-radius: var(--border-radius-md);
  padding: 0.875rem 1rem;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5px);
  resize: vertical;
  min-height: 120px;
}

.form-textarea-enhanced:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(15, 23, 42, 0.9);
  box-shadow: 0 0 0 4px rgba(173, 102, 16, 0.1), var(--shadow-md);
}

/* Enhanced Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.bg-glass {
  background: rgba(37, 37, 37, 0.8);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bg-glass-dark {
  background: rgba(18, 18, 18, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(173, 102, 16, 0.2);
}

.shadow-glow {
  box-shadow: 0 0 30px rgba(173, 102, 16, 0.3);
}

.shadow-glow-lg {
  box-shadow: 0 0 50px rgba(173, 102, 16, 0.4);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(var(--dark-800), var(--dark-800)) padding-box,
              var(--gradient-primary) border-box;
}

.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.hover-scale {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(173, 102, 16, 0.4);
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, var(--dark-700) 25%, var(--dark-600) 50%, var(--dark-700) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 20px rgba(173, 102, 16, 0.3);
  }
  100% {
    box-shadow: 0 0 40px rgba(173, 102, 16, 0.6);
  }
}

/* Modal Input Field Fixes */
.modal-container input[type="text"],
.modal-container input[type="password"],
.modal-container input[type="url"],
.modal-container input[type="email"],
.modal-container select,
.modal-container textarea {
  line-height: 1.4 !important;
  height: auto !important;
  min-height: 48px !important;
  box-sizing: border-box !important;
  vertical-align: middle !important;
  display: flex !important;
  align-items: center !important;
}

/* Special styling for select dropdowns */
.modal-container select {
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px !important;
  padding-right: 40px !important;
  cursor: pointer !important;
}

/* Ensure dropdown arrow is visible on focus */
.modal-container select:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ad6610' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
}

/* Icon positioning fixes for modal inputs */
.modal-container .relative i.ti-link,
.modal-container .relative i.ti-key {
  position: absolute !important;
  left: 12px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #9ca3af !important;
  z-index: 10 !important;
  pointer-events: none !important;
}

.modal-container .relative button {
  position: absolute !important;
  right: 8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
  color: #9ca3af !important;
  transition: color 0.2s ease !important;
}

.modal-container .relative button:hover {
  color: #ffffff !important;
}

/* Ensure text is properly visible in all input fields */
.modal-container input::placeholder,
.modal-container textarea::placeholder {
  color: rgba(156, 163, 175, 0.8) !important;
  opacity: 1 !important;
}

/* Video preview container fixes */
#editVideoPreview,
#editVideoPreviewMobile,
#videoPreview,
#videoPreviewMobile {
  background: #000 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

#editVideoPreview video,
#editVideoPreviewMobile video,
#videoPreview video,
#videoPreviewMobile video {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background: #000 !important;
}