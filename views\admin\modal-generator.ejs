<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">Modal Generator</h1>
      <p class="text-gray-400 mt-1">Generate streaming statistics modal for promotional content</p>
    </div>
    <div class="flex items-center space-x-4">
      <button onclick="generateRandomStats()" class="btn-secondary">
        <i class="ti ti-dice mr-2"></i>
        Generate Random
      </button>
      <button onclick="resetStats()" class="btn-outline">
        <i class="ti ti-refresh mr-2"></i>
        Reset
      </button>
    </div>
  </div>
</div>

<!-- Controls Panel -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
  <!-- Input Controls -->
  <div class="card-enhanced p-6">
    <h3 class="text-lg font-semibold text-white mb-4">Statistics Controls</h3>
    
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Penayangan</label>
        <input type="number" id="viewers" class="input-field" value="0" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Puncak tayangan serentak</label>
        <input type="number" id="peakViewers" class="input-field" value="0" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Total waktu tonton (jam)</label>
        <input type="number" id="watchTime" class="input-field" value="0.00" step="0.01" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Rasio chat</label>
        <input type="number" id="chatRatio" class="input-field" value="0" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Rata-rata durasi tonton (menit)</label>
        <input type="number" id="avgDuration" class="input-field" value="0.00" step="0.01" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Durasi stream (menit)</label>
        <input type="number" id="streamDuration" class="input-field" value="22.42" step="0.01" min="0">
      </div>
    </div>

    <div class="mt-4">
      <label class="block text-sm font-medium text-gray-300 mb-2">Stream Title</label>
      <input type="text" id="streamTitle" class="input-field" value="satisfying" placeholder="Enter stream title">
    </div>

    <div class="mt-4">
      <label class="block text-sm font-medium text-gray-300 mb-2">Channel Name</label>
      <input type="text" id="channelName" class="input-field" value="Skyram Official" placeholder="Enter channel name">
    </div>

    <div class="mt-4">
      <label class="block text-sm font-medium text-gray-300 mb-2">Product Cards (Optional)</label>
      <div class="space-y-2">
        <input type="text" id="product1" class="input-field text-xs" value="✅ MOBILE LEGENDS Starlight - Member Plus - CODE: SLPML - 💰 Rp 195.072" placeholder="Product 1">
        <input type="text" id="product2" class="input-field text-xs" value="✅ MOBILELEGEND - 875 Diamond - CODE: 8.7.5.DM - 💰 Rp 224.000" placeholder="Product 2">
        <input type="text" id="product3" class="input-field text-xs" value="✅ MOBILELEGEND - 2010 Diamond - CODE: 2.0.1.0.DM - 💰 Rp 487.150" placeholder="Product 3">
        <input type="text" id="product4" class="input-field text-xs" value="✅ MOBILELEGEND - 4830 Diamond - CODE: 4.8.3.0.DM - 💰 Rp 1.169.001" placeholder="Product 4">
      </div>
      <p class="text-xs text-gray-500 mt-1">Leave empty to hide product cards</p>
    </div>

    <div class="mt-4">
      <label class="block text-sm font-medium text-gray-300 mb-2">Thumbnail URL (Optional)</label>
      <input type="url" id="thumbnailUrl" class="input-field text-xs" placeholder="https://example.com/thumbnail.jpg">
      <p class="text-xs text-gray-500 mt-1">Leave empty for default placeholder</p>
    </div>
  </div>

  <!-- Quick Presets -->
  <div class="card-enhanced p-6">
    <h3 class="text-lg font-semibold text-white mb-4">Quick Presets</h3>
    
    <div class="grid grid-cols-1 gap-3">
      <button onclick="applyPreset('small')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Small Stream</span>
          <span class="text-xs text-gray-400">10-100 viewers</span>
        </div>
      </button>
      
      <button onclick="applyPreset('medium')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Medium Stream</span>
          <span class="text-xs text-gray-400">100-1K viewers</span>
        </div>
      </button>
      
      <button onclick="applyPreset('large')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Large Stream</span>
          <span class="text-xs text-gray-400">1K-10K viewers</span>
        </div>
      </button>
      
      <button onclick="applyPreset('viral')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Viral Stream</span>
          <span class="text-xs text-gray-400">10K+ viewers</span>
        </div>
      </button>

      <button onclick="applyPreset('gaming')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Gaming Stream</span>
          <span class="text-xs text-gray-400">Mobile Legends</span>
        </div>
      </button>

      <button onclick="applyPreset('topup')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Top-up Stream</span>
          <span class="text-xs text-gray-400">Diamond seller</span>
        </div>
      </button>
    </div>

    <div class="mt-6">
      <h4 class="text-md font-medium text-white mb-3">Theme Options</h4>
      <div class="grid grid-cols-2 gap-2 mb-4">
        <button onclick="setTheme('light')" class="btn-outline text-xs">
          Light Theme
        </button>
        <button onclick="setTheme('dark')" class="btn-outline text-xs">
          Dark Theme
        </button>
      </div>

      <h4 class="text-md font-medium text-white mb-3">Save/Load Preset</h4>
      <div class="flex space-x-2 mb-4">
        <button onclick="savePreset()" class="btn-outline flex-1 text-xs">
          <i class="ti ti-device-floppy mr-1"></i>
          Save Current
        </button>
        <button onclick="loadPreset()" class="btn-outline flex-1 text-xs">
          <i class="ti ti-upload mr-1"></i>
          Load Preset
        </button>
      </div>

      <h4 class="text-md font-medium text-white mb-3">Export Options</h4>
      <div class="flex space-x-2">
        <button onclick="exportAsImage()" class="btn-primary flex-1">
          <i class="ti ti-photo mr-2"></i>
          Export as Image
        </button>
        <button onclick="copyHTML()" class="btn-secondary flex-1">
          <i class="ti ti-copy mr-2"></i>
          Copy HTML
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Preview Modal -->
<div class="card-enhanced p-6">
  <h3 class="text-lg font-semibold text-white mb-4">Preview</h3>
  
  <!-- Modal Preview Container with Dark Background -->
  <div class="bg-black bg-opacity-90 rounded-lg p-8 relative min-h-96 flex items-center justify-center">

    <!-- YouTube-style Modal Content -->
    <div id="modal-preview" class="bg-gray-900 rounded-xl max-w-sm w-full relative shadow-2xl overflow-hidden" style="font-family: 'Roboto', sans-serif;">

      <!-- Header with close button -->
      <div class="flex items-center justify-between p-4 border-b border-gray-700">
        <h2 class="text-white text-lg font-medium">Streaming Selesai</h2>
        <button class="w-6 h-6 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-colors">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-gray-300">
            <path d="M13 17h-2v-6h2v6zm0-8h-2V7h2v2z"/>
            <circle cx="12" cy="12" r="10"/>
          </svg>
        </button>
      </div>

      <!-- Thumbnail Section -->
      <div class="p-4">
        <div class="relative mb-4">
          <div class="w-full h-40 bg-black rounded-lg overflow-hidden relative">
            <!-- Thumbnail placeholder -->
            <div id="thumbnail-container" class="w-full h-full bg-gradient-to-br from-gray-800 to-black flex items-center justify-center">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" class="text-gray-500">
                <polygon points="23 7 16 12 23 17 23 7"></polygon>
                <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
              </svg>
            </div>
            <!-- Video title overlay -->
            <div class="absolute bottom-3 left-3 right-3">
              <p class="text-white font-medium text-base mb-1" id="preview-title">satisfying</p>
              <p class="text-gray-300 text-sm" id="preview-channel">Skyram Official</p>
            </div>
          </div>
        </div>

        <!-- Product Cards Section -->
        <div id="product-cards" class="space-y-3 mb-6">
          <!-- Product cards will be generated by JavaScript -->
        </div>

        <!-- Statistics Grid -->
        <div class="grid grid-cols-3 gap-4 mb-6">
          <div class="text-center">
            <p class="text-gray-400 text-xs mb-1">Penayangan</p>
            <p class="text-white text-xl font-bold" id="preview-viewers">0</p>
          </div>

          <div class="text-center">
            <p class="text-gray-400 text-xs mb-1">Puncak tayang serentak</p>
            <p class="text-white text-xl font-bold" id="preview-peak">0</p>
          </div>

          <div class="text-center">
            <p class="text-gray-400 text-xs mb-1">Total waktu tonton</p>
            <p class="text-white text-xl font-bold" id="preview-watch-time">0.00</p>
          </div>

          <div class="text-center">
            <p class="text-gray-400 text-xs mb-1">Rasio chat</p>
            <p class="text-white text-xl font-bold" id="preview-chat">0</p>
          </div>

          <div class="text-center">
            <p class="text-gray-400 text-xs mb-1">Rata-rata durasi tonton</p>
            <p class="text-white text-xl font-bold" id="preview-avg">0.00</p>
          </div>

          <div class="text-center">
            <p class="text-gray-400 text-xs mb-1">Durasi</p>
            <p class="text-white text-xl font-bold" id="preview-duration">22.42</p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-3">
          <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2.5 rounded-full text-sm font-medium transition-colors">
            Tutup
          </button>
          <button class="flex-1 bg-white hover:bg-gray-100 text-black px-4 py-2.5 rounded-full text-sm font-medium transition-colors">
            Edit di Studio
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Update preview when inputs change
function updatePreview() {
  document.getElementById('preview-title').textContent = document.getElementById('streamTitle').value || 'satisfying';
  document.getElementById('preview-channel').textContent = document.getElementById('channelName').value || 'Skyram Official';
  document.getElementById('preview-viewers').textContent = document.getElementById('viewers').value || '0';
  document.getElementById('preview-peak').textContent = document.getElementById('peakViewers').value || '0';
  document.getElementById('preview-watch-time').textContent = document.getElementById('watchTime').value || '0.00';
  document.getElementById('preview-chat').textContent = document.getElementById('chatRatio').value || '0';
  document.getElementById('preview-avg').textContent = document.getElementById('avgDuration').value || '0.00';
  document.getElementById('preview-duration').textContent = document.getElementById('streamDuration').value || '22.42';

  // Update thumbnail
  updateThumbnail();

  // Update product cards
  updateProductCards();
}

// Update thumbnail based on URL input
function updateThumbnail() {
  const thumbnailUrl = document.getElementById('thumbnailUrl').value.trim();
  const thumbnailContainer = document.getElementById('thumbnail-container');

  if (thumbnailUrl) {
    thumbnailContainer.innerHTML = `
      <img src="${thumbnailUrl}" alt="Stream thumbnail" class="w-full h-full object-cover rounded-lg"
           onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
      <div class="w-full h-full bg-gradient-to-br from-gray-800 to-black flex items-center justify-center" style="display: none;">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" class="text-gray-500">
          <polygon points="23 7 16 12 23 17 23 7"></polygon>
          <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
        </svg>
      </div>
    `;
  } else {
    thumbnailContainer.innerHTML = `
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" class="text-gray-500">
        <polygon points="23 7 16 12 23 17 23 7"></polygon>
        <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
      </svg>
    `;
  }
}

// Update product cards based on input
function updateProductCards() {
  const productCardsContainer = document.getElementById('product-cards');
  productCardsContainer.innerHTML = '';

  for (let i = 1; i <= 4; i++) {
    const productInput = document.getElementById(`product${i}`);
    const productText = productInput.value.trim();

    if (productText) {
      const cardDiv = document.createElement('div');
      cardDiv.className = 'bg-gray-800 rounded-lg p-3 flex items-center space-x-3';

      // Parse product text to extract parts
      const parts = productText.split(' - ');
      const mainText = parts[0] || productText;
      const subTexts = parts.slice(1);

      // Create product image placeholder
      const productImage = `
        <div class="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center flex-shrink-0 border border-gray-600">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" class="text-gray-400">
            ${productText.includes('Diamond') ?
              '<path d="M6 3h12l4 6-10 13L2 9l4-6z"/>' :
              '<rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/>'
            }
          </svg>
        </div>
      `;

      cardDiv.innerHTML = `
        ${productImage}
        <div class="flex-1 min-w-0">
          <p class="text-white text-sm font-medium mb-1 truncate">${mainText}</p>
          ${subTexts.map(text => `<p class="text-gray-400 text-xs truncate">${text}</p>`).join('')}
        </div>
      `;

      productCardsContainer.appendChild(cardDiv);
    }
  }
}

// Add event listeners to all inputs
document.addEventListener('DOMContentLoaded', function() {
  const inputs = ['streamTitle', 'channelName', 'viewers', 'peakViewers', 'watchTime', 'chatRatio', 'avgDuration', 'streamDuration', 'product1', 'product2', 'product3', 'product4', 'thumbnailUrl'];
  inputs.forEach(id => {
    document.getElementById(id).addEventListener('input', updatePreview);
  });

  updatePreview(); // Initial update
});

// Generate random statistics
function generateRandomStats() {
  const viewers = Math.floor(Math.random() * 10000) + 50;
  const peakViewers = Math.floor(viewers * (1.2 + Math.random() * 0.8)); // 20-100% more than current
  const streamDuration = (Math.random() * 180 + 10).toFixed(2); // 10-190 minutes
  const watchTime = (viewers * streamDuration * (0.3 + Math.random() * 0.4) / 60).toFixed(2); // 30-70% retention
  const chatRatio = Math.floor(viewers * (0.05 + Math.random() * 0.15)); // 5-20% chat participation
  const avgDuration = (streamDuration * (0.2 + Math.random() * 0.6)).toFixed(2); // 20-80% of stream duration
  
  document.getElementById('viewers').value = viewers;
  document.getElementById('peakViewers').value = peakViewers;
  document.getElementById('watchTime').value = watchTime;
  document.getElementById('chatRatio').value = chatRatio;
  document.getElementById('avgDuration').value = avgDuration;
  document.getElementById('streamDuration').value = streamDuration;
  
  updatePreview();
}

// Apply presets
function applyPreset(type) {
  let config = {};
  
  switch(type) {
    case 'small':
      config = {
        viewers: Math.floor(Math.random() * 90) + 10, // 10-100
        streamDuration: (Math.random() * 60 + 15).toFixed(2) // 15-75 minutes
      };
      break;
    case 'medium':
      config = {
        viewers: Math.floor(Math.random() * 900) + 100, // 100-1000
        streamDuration: (Math.random() * 120 + 30).toFixed(2) // 30-150 minutes
      };
      break;
    case 'large':
      config = {
        viewers: Math.floor(Math.random() * 9000) + 1000, // 1000-10000
        streamDuration: (Math.random() * 180 + 60).toFixed(2) // 60-240 minutes
      };
      break;
    case 'viral':
      config = {
        viewers: Math.floor(Math.random() * 90000) + 10000, // 10000-100000
        streamDuration: (Math.random() * 300 + 120).toFixed(2) // 120-420 minutes
      };
      break;
    case 'gaming':
      config = {
        viewers: Math.floor(Math.random() * 2000) + 500, // 500-2500
        streamDuration: (Math.random() * 180 + 60).toFixed(2) // 60-240 minutes
      };
      // Set gaming-specific content
      document.getElementById('streamTitle').value = 'LIVE MOBILE LEGENDS - PUSH RANK MYTHIC';
      document.getElementById('channelName').value = 'Pro Gaming Channel';
      break;
    case 'topup':
      config = {
        viewers: Math.floor(Math.random() * 1500) + 200, // 200-1700
        streamDuration: (Math.random() * 120 + 30).toFixed(2) // 30-150 minutes
      };
      // Set topup-specific content
      document.getElementById('streamTitle').value = 'TOPUP DIAMOND MURAH - PROMO HARI INI';
      document.getElementById('channelName').value = 'Diamond Store Official';
      break;
  }
  
  const viewers = config.viewers;
  const streamDuration = parseFloat(config.streamDuration);
  const peakViewers = Math.floor(viewers * (1.2 + Math.random() * 0.8));
  const watchTime = (viewers * streamDuration * (0.3 + Math.random() * 0.4) / 60).toFixed(2);
  const chatRatio = Math.floor(viewers * (0.05 + Math.random() * 0.15));
  const avgDuration = (streamDuration * (0.2 + Math.random() * 0.6)).toFixed(2);
  
  document.getElementById('viewers').value = viewers;
  document.getElementById('peakViewers').value = peakViewers;
  document.getElementById('watchTime').value = watchTime;
  document.getElementById('chatRatio').value = chatRatio;
  document.getElementById('avgDuration').value = avgDuration;
  document.getElementById('streamDuration').value = streamDuration;
  
  updatePreview();
}

// Reset all stats
function resetStats() {
  document.getElementById('viewers').value = '0';
  document.getElementById('peakViewers').value = '0';
  document.getElementById('watchTime').value = '0.00';
  document.getElementById('chatRatio').value = '0';
  document.getElementById('avgDuration').value = '0.00';
  document.getElementById('streamDuration').value = '22.42';
  document.getElementById('streamTitle').value = 'satisfying';
  document.getElementById('channelName').value = 'Skyram Official';
  document.getElementById('thumbnailUrl').value = '';
  document.getElementById('product1').value = '✅ MOBILE LEGENDS Starlight - Member Plus - CODE: SLPML - 💰 Rp 195.072';
  document.getElementById('product2').value = '✅ MOBILELEGEND - 875 Diamond - CODE: 8.7.5.DM - 💰 Rp 224.000';
  document.getElementById('product3').value = '✅ MOBILELEGEND - 2010 Diamond - CODE: 2.0.1.0.DM - 💰 Rp 487.150';
  document.getElementById('product4').value = '✅ MOBILELEGEND - 4830 Diamond - CODE: 4.8.3.0.DM - 💰 Rp 1.169.001';

  updatePreview();
}

// Export as image (using html2canvas if available)
function exportAsImage() {
  const modal = document.getElementById('modal-preview');
  
  // Try to use html2canvas if available
  if (typeof html2canvas !== 'undefined') {
    html2canvas(modal, {
      backgroundColor: '#000000',
      scale: 2
    }).then(canvas => {
      const link = document.createElement('a');
      link.download = 'streaming-stats-modal.png';
      link.href = canvas.toDataURL();
      link.click();
    });
  } else {
    alert('Export feature requires html2canvas library. Please add it to use this feature.');
  }
}

// Copy HTML
function copyHTML() {
  const modal = document.getElementById('modal-preview');
  const html = modal.outerHTML;

  navigator.clipboard.writeText(html).then(() => {
    alert('HTML copied to clipboard!');
  }).catch(() => {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = html;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('HTML copied to clipboard!');
  });
}

// Set theme (light/dark)
function setTheme(theme) {
  const modal = document.getElementById('modal-preview');

  if (theme === 'dark') {
    modal.className = 'bg-gray-900 rounded-lg max-w-sm mx-auto relative shadow-2xl';
    modal.style.fontFamily = "'Roboto', sans-serif";

    // Update header
    const header = modal.querySelector('.flex.items-center.justify-between');
    header.className = 'flex items-center justify-between p-4 border-b border-gray-700';
    header.querySelector('h2').className = 'text-white text-lg font-medium';

    // Update main content
    const content = modal.querySelector('.p-4');
    content.className = 'p-4';

    // Update stats text colors
    modal.querySelectorAll('.text-gray-600').forEach(el => {
      el.className = el.className.replace('text-gray-600', 'text-gray-400');
    });
    modal.querySelectorAll('.text-gray-900').forEach(el => {
      el.className = el.className.replace('text-gray-900', 'text-white');
    });

    // Update buttons
    const buttons = modal.querySelectorAll('button');
    buttons[0].className = 'flex-1 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-full text-sm font-medium transition-colors';
    buttons[1].className = 'flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full text-sm font-medium transition-colors';

  } else {
    modal.className = 'bg-white rounded-lg max-w-sm mx-auto relative shadow-2xl';
    modal.style.fontFamily = "'Roboto', sans-serif";

    // Update header
    const header = modal.querySelector('.flex.items-center.justify-between');
    header.className = 'flex items-center justify-between p-4 border-b border-gray-200';
    header.querySelector('h2').className = 'text-gray-900 text-lg font-medium';

    // Update main content
    const content = modal.querySelector('.p-4');
    content.className = 'p-4';

    // Update stats text colors
    modal.querySelectorAll('.text-gray-400').forEach(el => {
      if (el.className.includes('text-gray-400') && !el.className.includes('text-3xl')) {
        el.className = el.className.replace('text-gray-400', 'text-gray-600');
      }
    });
    modal.querySelectorAll('.text-white').forEach(el => {
      if (!el.closest('.w-full.h-32')) { // Don't change overlay text
        el.className = el.className.replace('text-white', 'text-gray-900');
      }
    });

    // Update buttons
    const buttons = modal.querySelectorAll('button');
    buttons[0].className = 'flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm font-medium transition-colors';
    buttons[1].className = 'flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full text-sm font-medium transition-colors';
  }

  // Re-update product cards with new theme
  updateProductCards();
}

// Save current settings as preset
function savePreset() {
  const preset = {
    streamTitle: document.getElementById('streamTitle').value,
    channelName: document.getElementById('channelName').value,
    viewers: document.getElementById('viewers').value,
    peakViewers: document.getElementById('peakViewers').value,
    watchTime: document.getElementById('watchTime').value,
    chatRatio: document.getElementById('chatRatio').value,
    avgDuration: document.getElementById('avgDuration').value,
    streamDuration: document.getElementById('streamDuration').value,
    thumbnailUrl: document.getElementById('thumbnailUrl').value,
    product1: document.getElementById('product1').value,
    product2: document.getElementById('product2').value,
    product3: document.getElementById('product3').value,
    product4: document.getElementById('product4').value
  };

  const presetJson = JSON.stringify(preset, null, 2);
  const blob = new Blob([presetJson], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = 'modal-preset.json';
  link.click();
  URL.revokeObjectURL(url);
}

// Load preset from file
function loadPreset() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = function(event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const preset = JSON.parse(e.target.result);

          // Apply preset values
          Object.keys(preset).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
              element.value = preset[key];
            }
          });

          updatePreview();
          alert('Preset loaded successfully!');
        } catch (error) {
          alert('Error loading preset: Invalid JSON file');
        }
      };
      reader.readAsText(file);
    }
  };
  input.click();
}
</script>

<style>
/* YouTube-style modal enhancements */
#modal-preview {
  backdrop-filter: blur(10px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
}

/* Smooth transitions for all interactive elements */
#modal-preview button,
#modal-preview .bg-gray-800 {
  transition: all 0.2s ease-in-out;
}

/* Product card hover effects */
#product-cards .bg-gray-800:hover {
  background-color: #374151 !important;
  transform: translateY(-1px);
}

/* Better thumbnail styling */
#thumbnail-container {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Statistics text styling */
#modal-preview .text-xl {
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Button styling improvements */
#modal-preview button {
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Close button styling */
#modal-preview .w-6.h-6 {
  background: rgba(55, 65, 81, 0.8);
  backdrop-filter: blur(4px);
}

#modal-preview .w-6.h-6:hover {
  background: rgba(75, 85, 99, 0.9);
}

/* Modal container background */
.bg-black.bg-opacity-90 {
  background: radial-gradient(circle at center, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
}

/* Product card styling improvements */
#product-cards .bg-gray-800 {
  border: 1px solid rgba(75, 85, 99, 0.3);
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

/* Thumbnail overlay text shadow */
#modal-preview .absolute.bottom-3 p {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}
</style>
