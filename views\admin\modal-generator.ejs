<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">Modal Generator</h1>
      <p class="text-gray-400 mt-1">Generate streaming statistics modal for promotional content</p>
    </div>
    <div class="flex items-center space-x-4">
      <button onclick="generateRandomStats()" class="btn-secondary">
        <i class="ti ti-dice mr-2"></i>
        Generate Random
      </button>
      <button onclick="resetStats()" class="btn-outline">
        <i class="ti ti-refresh mr-2"></i>
        Reset
      </button>
    </div>
  </div>
</div>

<!-- Controls Panel -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
  <!-- Input Controls -->
  <div class="card-enhanced p-6">
    <h3 class="text-lg font-semibold text-white mb-4">Statistics Controls</h3>
    
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Penayangan</label>
        <input type="number" id="viewers" class="input-field" value="0" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Puncak tayangan serentak</label>
        <input type="number" id="peakViewers" class="input-field" value="0" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Total waktu tonton (jam)</label>
        <input type="number" id="watchTime" class="input-field" value="0.00" step="0.01" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Rasio chat</label>
        <input type="number" id="chatRatio" class="input-field" value="0" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Rata-rata durasi tonton (menit)</label>
        <input type="number" id="avgDuration" class="input-field" value="0.00" step="0.01" min="0">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Durasi stream (menit)</label>
        <input type="number" id="streamDuration" class="input-field" value="22.42" step="0.01" min="0">
      </div>
    </div>


  </div>

  <!-- Quick Presets -->
  <div class="card-enhanced p-6">
    <h3 class="text-lg font-semibold text-white mb-4">Quick Presets</h3>
    
    <div class="grid grid-cols-1 gap-3">
      <button onclick="applyPreset('small')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Small Stream</span>
          <span class="text-xs text-gray-400">10-100 viewers</span>
        </div>
      </button>
      
      <button onclick="applyPreset('medium')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Medium Stream</span>
          <span class="text-xs text-gray-400">100-1K viewers</span>
        </div>
      </button>
      
      <button onclick="applyPreset('large')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Large Stream</span>
          <span class="text-xs text-gray-400">1K-10K viewers</span>
        </div>
      </button>
      
      <button onclick="applyPreset('viral')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Viral Stream</span>
          <span class="text-xs text-gray-400">10K+ viewers</span>
        </div>
      </button>

      <button onclick="applyPreset('gaming')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Gaming Stream</span>
          <span class="text-xs text-gray-400">Mobile Legends</span>
        </div>
      </button>

      <button onclick="applyPreset('topup')" class="btn-outline text-left">
        <div class="flex justify-between items-center">
          <span>Top-up Stream</span>
          <span class="text-xs text-gray-400">Diamond seller</span>
        </div>
      </button>
    </div>

    <div class="mt-6">
      <h4 class="text-md font-medium text-white mb-3">Theme Options</h4>
      <div class="grid grid-cols-2 gap-2 mb-4">
        <button onclick="setTheme('light')" class="btn-outline text-xs">
          Light Theme
        </button>
        <button onclick="setTheme('dark')" class="btn-outline text-xs">
          Dark Theme
        </button>
      </div>

      <h4 class="text-md font-medium text-white mb-3">Save/Load Preset</h4>
      <div class="flex space-x-2 mb-4">
        <button onclick="savePreset()" class="btn-outline flex-1 text-xs">
          <i class="ti ti-device-floppy mr-1"></i>
          Save Current
        </button>
        <button onclick="loadPreset()" class="btn-outline flex-1 text-xs">
          <i class="ti ti-upload mr-1"></i>
          Load Preset
        </button>
      </div>

      <h4 class="text-md font-medium text-white mb-3">Export Options</h4>
      <div class="flex space-x-2">
        <button onclick="exportAsImage()" class="btn-primary flex-1">
          <i class="ti ti-photo mr-2"></i>
          Export as Image
        </button>
        <button onclick="copyHTML()" class="btn-secondary flex-1">
          <i class="ti ti-copy mr-2"></i>
          Copy HTML
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Preview Modal -->
<div class="card-enhanced p-6">
  <h3 class="text-lg font-semibold text-white mb-4">Preview</h3>
  
  <!-- Modal Preview Container with Dark Background -->
  <div class="bg-black bg-opacity-90 rounded-lg p-8 relative min-h-96 flex items-center justify-center">

    <!-- YouTube-style Modal Content -->
    <div id="modal-preview" class="bg-gray-900 rounded-xl max-w-2xl w-full relative shadow-2xl overflow-hidden" style="font-family: 'Roboto', sans-serif;">

      <!-- Statistics Section Only -->
      <div class="px-8 py-6">
        <!-- Statistics Grid -->
        <div class="grid grid-cols-3 gap-x-8 gap-y-4 mb-4">
          <div class="text-left">
            <p class="text-gray-400 text-sm mb-2 font-normal">Penayangan</p>
            <p class="text-white text-4xl font-bold" id="preview-viewers">0</p>
          </div>

          <div class="text-left">
            <p class="text-gray-400 text-sm mb-2 font-normal">Puncak tayang serentak</p>
            <p class="text-white text-4xl font-bold" id="preview-peak">0</p>
          </div>

          <div class="text-left">
            <p class="text-gray-400 text-sm mb-2 font-normal">Total waktu tonton</p>
            <p class="text-white text-4xl font-bold" id="preview-watch-time">0.00</p>
          </div>

          <div class="text-left">
            <p class="text-gray-400 text-sm mb-2 font-normal">Rasio chat</p>
            <p class="text-white text-4xl font-bold" id="preview-chat">0</p>
          </div>

          <div class="text-left">
            <p class="text-gray-400 text-sm mb-2 font-normal">Rata-rata durasi tonton</p>
            <p class="text-white text-4xl font-bold" id="preview-avg">0.00</p>
          </div>

          <div class="text-left">
            <p class="text-gray-400 text-sm mb-2 font-normal">Durasi</p>
            <p class="text-white text-4xl font-bold" id="preview-duration">22.42</p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-3" style="justify-content: center; margin-left: 120px;">
          <button class="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-full text-sm font-medium transition-colors">
            Tutup
          </button>
          <button class="bg-white hover:bg-gray-100 text-black px-6 py-2 rounded-full text-sm font-medium transition-colors">
            Edit di Studio
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Update preview when inputs change
function updatePreview() {
  document.getElementById('preview-viewers').textContent = document.getElementById('viewers').value || '0';
  document.getElementById('preview-peak').textContent = document.getElementById('peakViewers').value || '0';
  document.getElementById('preview-watch-time').textContent = document.getElementById('watchTime').value || '0.00';
  document.getElementById('preview-chat').textContent = document.getElementById('chatRatio').value || '0';
  document.getElementById('preview-avg').textContent = document.getElementById('avgDuration').value || '0.00';
  document.getElementById('preview-duration').textContent = document.getElementById('streamDuration').value || '22.42';
}



// Add event listeners to all inputs
document.addEventListener('DOMContentLoaded', function() {
  const inputs = ['viewers', 'peakViewers', 'watchTime', 'chatRatio', 'avgDuration', 'streamDuration'];
  inputs.forEach(id => {
    document.getElementById(id).addEventListener('input', updatePreview);
  });

  updatePreview(); // Initial update
});

// Generate random statistics
function generateRandomStats() {
  const viewers = Math.floor(Math.random() * 10000) + 50;
  const peakViewers = Math.floor(viewers * (1.2 + Math.random() * 0.8)); // 20-100% more than current
  const streamDuration = (Math.random() * 180 + 10).toFixed(2); // 10-190 minutes
  const watchTime = (viewers * streamDuration * (0.3 + Math.random() * 0.4) / 60).toFixed(2); // 30-70% retention
  const chatRatio = Math.floor(viewers * (0.05 + Math.random() * 0.15)); // 5-20% chat participation
  const avgDuration = (streamDuration * (0.2 + Math.random() * 0.6)).toFixed(2); // 20-80% of stream duration
  
  document.getElementById('viewers').value = viewers;
  document.getElementById('peakViewers').value = peakViewers;
  document.getElementById('watchTime').value = watchTime;
  document.getElementById('chatRatio').value = chatRatio;
  document.getElementById('avgDuration').value = avgDuration;
  document.getElementById('streamDuration').value = streamDuration;
  
  updatePreview();
}

// Apply presets
function applyPreset(type) {
  let config = {};
  
  switch(type) {
    case 'small':
      config = {
        viewers: Math.floor(Math.random() * 90) + 10, // 10-100
        streamDuration: (Math.random() * 60 + 15).toFixed(2) // 15-75 minutes
      };
      break;
    case 'medium':
      config = {
        viewers: Math.floor(Math.random() * 900) + 100, // 100-1000
        streamDuration: (Math.random() * 120 + 30).toFixed(2) // 30-150 minutes
      };
      break;
    case 'large':
      config = {
        viewers: Math.floor(Math.random() * 9000) + 1000, // 1000-10000
        streamDuration: (Math.random() * 180 + 60).toFixed(2) // 60-240 minutes
      };
      break;
    case 'viral':
      config = {
        viewers: Math.floor(Math.random() * 90000) + 10000, // 10000-100000
        streamDuration: (Math.random() * 300 + 120).toFixed(2) // 120-420 minutes
      };
      break;
    case 'gaming':
      config = {
        viewers: Math.floor(Math.random() * 2000) + 500, // 500-2500
        streamDuration: (Math.random() * 180 + 60).toFixed(2) // 60-240 minutes
      };
      break;
    case 'topup':
      config = {
        viewers: Math.floor(Math.random() * 1500) + 200, // 200-1700
        streamDuration: (Math.random() * 120 + 30).toFixed(2) // 30-150 minutes
      };
      break;
  }
  
  const viewers = config.viewers;
  const streamDuration = parseFloat(config.streamDuration);
  const peakViewers = Math.floor(viewers * (1.2 + Math.random() * 0.8));
  const watchTime = (viewers * streamDuration * (0.3 + Math.random() * 0.4) / 60).toFixed(2);
  const chatRatio = Math.floor(viewers * (0.05 + Math.random() * 0.15));
  const avgDuration = (streamDuration * (0.2 + Math.random() * 0.6)).toFixed(2);
  
  document.getElementById('viewers').value = viewers;
  document.getElementById('peakViewers').value = peakViewers;
  document.getElementById('watchTime').value = watchTime;
  document.getElementById('chatRatio').value = chatRatio;
  document.getElementById('avgDuration').value = avgDuration;
  document.getElementById('streamDuration').value = streamDuration;
  
  updatePreview();
}

// Reset all stats
function resetStats() {
  document.getElementById('viewers').value = '0';
  document.getElementById('peakViewers').value = '0';
  document.getElementById('watchTime').value = '0.00';
  document.getElementById('chatRatio').value = '0';
  document.getElementById('avgDuration').value = '0.00';
  document.getElementById('streamDuration').value = '22.42';

  updatePreview();
}

// Export as image (using html2canvas if available)
function exportAsImage() {
  const modal = document.getElementById('modal-preview');
  
  // Try to use html2canvas if available
  if (typeof html2canvas !== 'undefined') {
    html2canvas(modal, {
      backgroundColor: '#000000',
      scale: 2
    }).then(canvas => {
      const link = document.createElement('a');
      link.download = 'streaming-stats-modal.png';
      link.href = canvas.toDataURL();
      link.click();
    });
  } else {
    alert('Export feature requires html2canvas library. Please add it to use this feature.');
  }
}

// Copy HTML
function copyHTML() {
  const modal = document.getElementById('modal-preview');
  const html = modal.outerHTML;

  navigator.clipboard.writeText(html).then(() => {
    alert('HTML copied to clipboard!');
  }).catch(() => {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = html;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('HTML copied to clipboard!');
  });
}

// Set theme (light/dark)
function setTheme(theme) {
  const modal = document.getElementById('modal-preview');

  if (theme === 'dark') {
    modal.className = 'bg-gray-900 rounded-lg max-w-sm mx-auto relative shadow-2xl';
    modal.style.fontFamily = "'Roboto', sans-serif";

    // Update header
    const header = modal.querySelector('.flex.items-center.justify-between');
    header.className = 'flex items-center justify-between p-4 border-b border-gray-700';
    header.querySelector('h2').className = 'text-white text-lg font-medium';

    // Update main content
    const content = modal.querySelector('.p-4');
    content.className = 'p-4';

    // Update stats text colors
    modal.querySelectorAll('.text-gray-600').forEach(el => {
      el.className = el.className.replace('text-gray-600', 'text-gray-400');
    });
    modal.querySelectorAll('.text-gray-900').forEach(el => {
      el.className = el.className.replace('text-gray-900', 'text-white');
    });

    // Update buttons
    const buttons = modal.querySelectorAll('button');
    buttons[0].className = 'flex-1 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-full text-sm font-medium transition-colors';
    buttons[1].className = 'flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full text-sm font-medium transition-colors';

  } else {
    modal.className = 'bg-white rounded-lg max-w-sm mx-auto relative shadow-2xl';
    modal.style.fontFamily = "'Roboto', sans-serif";

    // Update header
    const header = modal.querySelector('.flex.items-center.justify-between');
    header.className = 'flex items-center justify-between p-4 border-b border-gray-200';
    header.querySelector('h2').className = 'text-gray-900 text-lg font-medium';

    // Update main content
    const content = modal.querySelector('.p-4');
    content.className = 'p-4';

    // Update stats text colors
    modal.querySelectorAll('.text-gray-400').forEach(el => {
      if (el.className.includes('text-gray-400') && !el.className.includes('text-3xl')) {
        el.className = el.className.replace('text-gray-400', 'text-gray-600');
      }
    });
    modal.querySelectorAll('.text-white').forEach(el => {
      if (!el.closest('.w-full.h-32')) { // Don't change overlay text
        el.className = el.className.replace('text-white', 'text-gray-900');
      }
    });

    // Update buttons
    const buttons = modal.querySelectorAll('button');
    buttons[0].className = 'flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm font-medium transition-colors';
    buttons[1].className = 'flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full text-sm font-medium transition-colors';
  }

}

// Save current settings as preset
function savePreset() {
  const preset = {
    viewers: document.getElementById('viewers').value,
    peakViewers: document.getElementById('peakViewers').value,
    watchTime: document.getElementById('watchTime').value,
    chatRatio: document.getElementById('chatRatio').value,
    avgDuration: document.getElementById('avgDuration').value,
    streamDuration: document.getElementById('streamDuration').value
  };

  const presetJson = JSON.stringify(preset, null, 2);
  const blob = new Blob([presetJson], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = 'modal-preset.json';
  link.click();
  URL.revokeObjectURL(url);
}

// Load preset from file
function loadPreset() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = function(event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const preset = JSON.parse(e.target.result);

          // Apply preset values
          Object.keys(preset).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
              element.value = preset[key];
            }
          });

          updatePreview();
          alert('Preset loaded successfully!');
        } catch (error) {
          alert('Error loading preset: Invalid JSON file');
        }
      };
      reader.readAsText(file);
    }
  };
  input.click();
}
</script>

<style>
/* YouTube-style modal enhancements */
#modal-preview {
  backdrop-filter: blur(10px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
}

/* Smooth transitions for all interactive elements */
#modal-preview button {
  transition: all 0.2s ease-in-out;
}



/* Statistics text styling */
#modal-preview .text-4xl {
  font-weight: 700;
  letter-spacing: -0.05em;
  line-height: 1.1;
}

#modal-preview .text-sm {
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 1.4;
}

/* Button styling improvements */
#modal-preview button {
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Close button styling */
#modal-preview .w-6.h-6 {
  background: rgba(55, 65, 81, 0.8);
  backdrop-filter: blur(4px);
}

#modal-preview .w-6.h-6:hover {
  background: rgba(75, 85, 99, 0.9);
}

/* Modal container background */
.bg-black.bg-opacity-90 {
  background: radial-gradient(circle at center, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
}

/* Product card styling improvements */
#product-cards .bg-gray-800 {
  border: 1px solid rgba(75, 85, 99, 0.3);
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

/* Thumbnail overlay text shadow */
#modal-preview .absolute.bottom-3 p {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}
</style>
