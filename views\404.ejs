<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Page Not Found - StreamOnPod</title>
  <link rel="icon" href="/images/streamonpod-logo.png" type="image/png">
  <link rel="alternate icon" href="/images/favicon.ico" type="image/x-icon">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">
  <link rel="stylesheet" href="/css/styles.css">

  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'inter': ['Inter', 'sans-serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif']
        },
        extend: {
          colors: {
            'primary': '#ad6610',
            'secondary': '#8b5209',
            'dark': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '100': '#E5E5E5',
            }
          }
        }
      }
    }
  </script>
</head>

<body class="bg-dark-900 text-white font-inter">
  <div class="min-h-screen flex items-center justify-center px-6">
    <div class="w-full max-w-lg bg-dark-800 rounded-xl shadow-xl p-8 text-center">
      
      <!-- Logo -->
      <div class="flex justify-center mb-8">
        <img src="/images/streamonpod-logotype.png" alt="StreamOnPod Logo" class="h-12">
      </div>

      <!-- 404 Icon -->
      <div class="flex justify-center mb-6">
        <div class="w-20 h-20 rounded-full bg-primary/20 flex items-center justify-center">
          <i class="ti ti-error-404 text-primary text-4xl"></i>
        </div>
      </div>

      <!-- Content -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold mb-2">404</h1>
        <h2 class="text-xl font-semibold mb-4 text-gray-300">Page Not Found</h2>
        <p class="text-gray-400 mb-6 leading-relaxed">
          Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-3">
        <a href="/" 
           class="block w-full bg-primary hover:bg-secondary text-white py-3 px-6 rounded-lg font-medium transition-colors">
          <i class="ti ti-home mr-2"></i>
          Back to Home
        </a>
        
        <% if (typeof user !== 'undefined' && user) { %>
        <a href="/dashboard" 
           class="block w-full bg-dark-700 hover:bg-dark-600 text-white py-3 px-6 rounded-lg font-medium transition-colors">
          <i class="ti ti-dashboard mr-2"></i>
          Go to Dashboard
        </a>
        <% } else { %>
        <a href="/login" 
           class="block w-full bg-dark-700 hover:bg-dark-600 text-white py-3 px-6 rounded-lg font-medium transition-colors">
          <i class="ti ti-login mr-2"></i>
          Login
        </a>
        <% } %>
      </div>

      <!-- Additional Help -->
      <div class="mt-8 pt-6 border-t border-dark-600">
        <p class="text-sm text-gray-500 mb-3">Need help?</p>
        <div class="flex justify-center space-x-4 text-sm">
          <a href="https://t.me/streamonpod" target="_blank" 
             class="text-primary hover:text-secondary transition-colors">
            <i class="ti ti-brand-telegram mr-1"></i>
            Contact Support
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Optional: Add some animation -->
  <style>
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .w-full.max-w-lg {
      animation: fadeInUp 0.6s ease-out;
    }
  </style>
</body>
</html>
