const express = require('express');
const router = express.Router();
const midtransService = require('../services/midtrans');
const Transaction = require('../models/Transaction');
const Subscription = require('../models/Subscription');
const User = require('../models/User');

// Authentication middleware
const isAuthenticated = (req, res, next) => {
  if (req.session.userId) {
    return next();
  }
  res.redirect('/login');
};

// Create payment transaction
router.post('/create', isAuthenticated, async (req, res) => {
  try {
    console.log('💳 Payment creation request received:', req.body);
    const { planId } = req.body;

    if (!planId) {
      console.log('❌ No plan ID provided for payment');
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    console.log('🔍 Looking for plan for payment:', planId);
    // Get plan details
    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      console.log('❌ Plan not found for payment:', planId);
      return res.status(404).json({ error: 'Plan not found' });
    }

    console.log('✅ Plan found for payment:', plan.name, 'Price:', plan.price);

    // Skip payment for free plans
    if (plan.price === 0) {
      console.log('❌ Attempted to create payment for free plan');
      return res.status(400).json({ error: 'Free plans do not require payment' });
    }

    // Get user details
    console.log('🔍 Looking for user:', req.session.userId);
    const user = await User.findById(req.session.userId);
    if (!user) {
      console.log('❌ User not found for payment:', req.session.userId);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('✅ User found for payment:', user.username);

    // Calculate prorated upgrade pricing
    console.log('🧮 Calculating prorated upgrade pricing...');
    const proratedCalculation = await Subscription.calculateProratedUpgrade(req.session.userId, planId);
    console.log('🧮 Prorated calculation:', proratedCalculation);

    // Use total payment amount (includes admin fee)
    const amountIDR = midtransService.validateIDRAmount(proratedCalculation.totalPayment);
    console.log('💱 Final payment amount (including admin fee):', amountIDR, 'IDR');

    // Generate unique order ID
    console.log('🆔 Generating order ID...');
    const orderId = Transaction.generateOrderId(req.session.userId, planId);
    console.log('🆔 Generated order ID:', orderId);

    // Prepare customer details
    const customerDetails = {
      first_name: user.username,
      email: user.email || `${user.username}@streamonpod.com`,
      phone: user.phone || '+62000000000'
    };

    // Prepare item details with upgrade information
    const itemName = proratedCalculation.isUpgrade
      ? `StreamOnPod ${plan.name} Plan - Upgrade`
      : `StreamOnPod ${plan.name} Plan - ${plan.billing_period}`;

    const itemDetails = [{
      id: plan.id,
      price: amountIDR,
      quantity: 1,
      name: itemName,
      category: 'subscription'
    }];

    // Create Midtrans transaction
    console.log('🏦 Creating Midtrans transaction...');
    console.log('🏦 Transaction details:', { orderId, amount: amountIDR, customerDetails, itemDetails });
    const midtransResult = await midtransService.createTransaction({
      orderId,
      amount: amountIDR,
      customerDetails,
      itemDetails
    });

    console.log('🏦 Midtrans result:', midtransResult);

    if (!midtransResult.success) {
      console.log('❌ Midtrans transaction failed:', midtransResult.error);
      return res.status(500).json({ error: 'Failed to create payment transaction' });
    }

    console.log('✅ Midtrans transaction created successfully');

    // Save transaction to database
    const transaction = await Transaction.create({
      user_id: req.session.userId,
      plan_id: planId,
      order_id: orderId,
      amount_idr: amountIDR,
      payment_method: 'midtrans',
      status: 'pending',
      midtrans_token: midtransResult.token,
      midtrans_redirect_url: midtransResult.redirect_url
    });

    const responseData = {
      success: true,
      transaction_id: transaction.id,
      order_id: orderId,
      snap_token: midtransResult.token,
      redirect_url: midtransResult.redirect_url,
      amount_idr: amountIDR,
      amount_formatted: midtransService.formatIDR(amountIDR),
      // Include prorated calculation details for frontend
      prorated_details: {
        isUpgrade: proratedCalculation.isUpgrade,
        currentPlan: proratedCalculation.currentPlan?.name || null,
        newPlan: proratedCalculation.newPlan.name,
        remainingDays: proratedCalculation.remainingDays,
        savings: proratedCalculation.savings,
        upgradePrice: proratedCalculation.upgradePrice,
        adminFee: proratedCalculation.adminFee,
        totalPayment: proratedCalculation.totalPayment
      }
    };

    console.log('📤 Sending response to frontend:', JSON.stringify(responseData, null, 2));
    res.status(200).json(responseData);

  } catch (error) {
    console.error('❌ Payment creation error:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
    res.status(500).json({ error: 'Failed to create payment' });
  }
});

// Get prorated upgrade calculation
router.post('/calculate-upgrade', isAuthenticated, async (req, res) => {
  try {
    const { planId } = req.body;

    if (!planId) {
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    // Calculate prorated upgrade pricing
    const proratedCalculation = await Subscription.calculateProratedUpgrade(req.session.userId, planId);

    res.json({
      success: true,
      calculation: {
        isUpgrade: proratedCalculation.isUpgrade,
        currentPlan: proratedCalculation.currentPlan?.name || null,
        newPlan: proratedCalculation.newPlan.name,
        remainingDays: proratedCalculation.remainingDays,
        remainingValue: proratedCalculation.remainingValue,
        newPlanPrice: proratedCalculation.newPlanPrice,
        upgradePrice: proratedCalculation.upgradePrice,
        adminFee: proratedCalculation.adminFee,
        totalPayment: proratedCalculation.totalPayment,
        savings: proratedCalculation.savings,
        // Format currency for display
        remainingValueFormatted: midtransService.formatIDR(proratedCalculation.remainingValue),
        newPlanPriceFormatted: midtransService.formatIDR(proratedCalculation.newPlanPrice),
        upgradePriceFormatted: midtransService.formatIDR(proratedCalculation.upgradePrice),
        adminFeeFormatted: midtransService.formatIDR(proratedCalculation.adminFee),
        totalPaymentFormatted: midtransService.formatIDR(proratedCalculation.totalPayment),
        savingsFormatted: midtransService.formatIDR(proratedCalculation.savings)
      }
    });

  } catch (error) {
    console.error('Calculate upgrade error:', error);
    res.status(500).json({
      error: error.message || 'Failed to calculate upgrade pricing'
    });
  }
});

// Middleware to bypass Cloudflare protection for webhook endpoints
const bypassCloudflareProtection = (req, res, next) => {
  // Set headers to bypass Cloudflare caching and protection
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'X-Robots-Tag': 'noindex, nofollow',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  next();
};

// Simple test endpoint without any middleware
router.get('/test', (req, res) => {
  res.status(200).send('OK');
});

// Test endpoint for webhook debugging
router.get('/notification/test', bypassCloudflareProtection, (req, res) => {
  console.log('🧪 Webhook test endpoint accessed');
  console.log('🌐 Request headers:', JSON.stringify(req.headers, null, 2));

  res.json({
    success: true,
    message: 'Webhook endpoint is accessible',
    timestamp: new Date().toISOString(),
    server: 'StreamOnPod Payment Service',
    cloudflare: {
      country: req.headers['cf-ipcountry'] || 'unknown',
      ray: req.headers['cf-ray'] || 'unknown',
      ip: req.headers['cf-connecting-ip'] || req.ip || 'unknown'
    },
    request_info: {
      user_agent: req.headers['user-agent'],
      method: req.method,
      url: req.url
    }
  });
});

// Alternative simple test endpoint
router.all('/ping', (req, res) => {
  res.set({
    'Cache-Control': 'no-cache',
    'Access-Control-Allow-Origin': '*'
  });
  res.status(200).json({
    status: 'pong',
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Special endpoint for Midtrans test notification (bypass signature validation)
router.post('/notification/midtrans-test', bypassCloudflareProtection, (req, res) => {
  console.log('🧪 Midtrans test notification received');
  console.log('📦 Request body:', JSON.stringify(req.body, null, 2));
  console.log('🌐 Request headers:', JSON.stringify(req.headers, null, 2));

  // Always return success for Midtrans test
  res.status(200).json({
    success: true,
    message: 'Test notification received successfully',
    timestamp: new Date().toISOString(),
    server: 'StreamOnPod Payment Service',
    test_mode: true,
    received_data: {
      body: req.body,
      headers: {
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent'],
        'x-forwarded-for': req.headers['x-forwarded-for']
      }
    }
  });
});

// Handle payment notification from Midtrans
router.post('/notification', bypassCloudflareProtection, async (req, res) => {
  try {
    console.log('🔔 Payment notification received from Midtrans');
    console.log('🌐 Request headers:', JSON.stringify(req.headers, null, 2));
    console.log('📦 Notification data:', JSON.stringify(req.body, null, 2));

    const notification = req.body;

    // Log for debugging
    console.log('🔍 Verifying signature...');

    // Verify signature
    if (!midtransService.verifySignature(notification)) {
      console.error('❌ Invalid signature from Midtrans notification');
      console.error('📋 Notification data:', notification);

      // For Midtrans test notifications, provide helpful response
      if (req.headers['user-agent']?.includes('Midtrans') ||
          !notification.order_id ||
          notification.transaction_status === 'test') {
        return res.status(200).json({
          success: false,
          error: 'Invalid signature',
          message: 'This appears to be a test notification. Use the test endpoint for testing.',
          test_endpoint: 'https://streamonpod.imthe.one/payment/notification/midtrans-test',
          received_data: notification
        });
      }

      return res.status(400).json({ error: 'Invalid signature' });
    }

    console.log('✅ Signature verified successfully');

    const { order_id, transaction_status, payment_type, transaction_time, transaction_id } = notification;
    console.log('📋 Processing notification for order:', order_id, 'status:', transaction_status);

    // Find transaction
    const transaction = await Transaction.findByOrderId(order_id);
    if (!transaction) {
      console.error('❌ Transaction not found:', order_id);

      // Check if this is a test notification from Midtrans Dashboard
      if (order_id.includes('payment_notif_test_') ||
          order_id.includes('_test_') ||
          req.headers['user-agent']?.includes('Veritrans')) {
        console.log('🧪 Detected test notification from Midtrans Dashboard');
        return res.status(200).send('Test OK');
      }

      return res.status(404).json({ error: 'Transaction not found' });
    }

    console.log('✅ Transaction found:', transaction.id);

    // Update transaction status based on Midtrans status
    let newStatus = 'pending';
    let shouldActivateSubscription = false;

    switch (transaction_status) {
      case 'capture':
      case 'settlement':
        newStatus = 'success';
        shouldActivateSubscription = true;
        break;
      case 'pending':
        newStatus = 'pending';
        break;
      case 'deny':
      case 'cancel':
      case 'expire':
        newStatus = 'failed';
        break;
      case 'refund':
        newStatus = 'refunded';
        break;
      default:
        newStatus = 'unknown';
    }

    // Update transaction
    console.log('🔄 Updating transaction status to:', newStatus);
    await Transaction.updateStatus(order_id, newStatus, {
      midtrans_transaction_id: transaction_id,
      midtrans_payment_type: payment_type,
      midtrans_transaction_time: transaction_time
    });
    console.log('✅ Transaction status updated');

    // Activate subscription if payment successful
    if (shouldActivateSubscription) {
      console.log('🎯 Activating subscription for successful payment...');
      await activateSubscription(transaction);
      console.log('🎉 Subscription activation completed');
    }

    console.log('📤 Sending success response to Midtrans');
    res.json({ success: true });

  } catch (error) {
    console.error('Payment notification error:', error);
    res.status(500).json({ error: 'Failed to process notification' });
  }
});

// Payment finish page (redirect from Midtrans)
router.get('/finish', isAuthenticated, async (req, res) => {
  try {
    const { order_id, status_code, transaction_status } = req.query;

    if (order_id) {
      // Get transaction status from Midtrans
      const statusResult = await midtransService.getTransactionStatus(order_id);
      
      if (statusResult.success) {
        const { transaction_status: currentStatus } = statusResult.data;
        
        // Find transaction
        const transaction = await Transaction.findByOrderId(order_id);
        
        if (transaction) {
          // Update status if needed
          if (currentStatus === 'settlement' || currentStatus === 'capture') {
            await Transaction.updateStatus(order_id, 'success');
            await activateSubscription(transaction);
          }
        }
      }
    }

    // Redirect to subscription page with status
    const redirectUrl = `/subscription/plans?payment=${transaction_status || 'unknown'}`;
    res.redirect(redirectUrl);

  } catch (error) {
    console.error('Payment finish error:', error);
    res.redirect('/subscription/plans?payment=error');
  }
});

// Get transaction status
router.get('/status/:orderId', isAuthenticated, async (req, res) => {
  try {
    const { orderId } = req.params;

    const transaction = await Transaction.findByOrderId(orderId);
    if (!transaction) {
      return res.status(404).json({ error: 'Transaction not found' });
    }

    // Check if user owns this transaction
    if (transaction.user_id !== req.session.userId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      success: true,
      status: transaction.status, // Add status at root level for frontend
      transaction: {
        id: transaction.id,
        order_id: transaction.order_id,
        status: transaction.status,
        amount_idr: transaction.amount_idr,
        amount_formatted: midtransService.formatIDR(transaction.amount_idr),
        plan_name: transaction.plan_name,
        created_at: transaction.created_at
      }
    });

  } catch (error) {
    console.error('Get transaction status error:', error);
    res.status(500).json({ error: 'Failed to get transaction status' });
  }
});

// Continue pending payment
router.post('/continue/:orderId', isAuthenticated, async (req, res) => {
  try {
    const { orderId } = req.params;

    console.log('🔄 Continuing payment for order:', orderId);

    const transaction = await Transaction.findByOrderId(orderId);
    if (!transaction) {
      console.log('❌ Transaction not found:', orderId);
      return res.status(404).json({ error: 'Transaction not found' });
    }

    // Check if user owns this transaction
    if (transaction.user_id !== req.session.userId) {
      console.log('❌ Access denied for user:', req.session.userId);
      return res.status(403).json({ error: 'Access denied' });
    }

    // Check if transaction is still pending and not expired
    if (transaction.status !== 'pending') {
      console.log('❌ Transaction is not pending:', transaction.status);
      return res.status(400).json({ error: 'Transaction is not pending' });
    }

    // Check if transaction is not expired (within 10 minutes)
    const createdAt = new Date(transaction.created_at);
    const now = new Date();
    const diffMinutes = (now - createdAt) / (1000 * 60);

    if (diffMinutes > 10) {
      console.log('❌ Transaction expired:', diffMinutes, 'minutes ago');
      // Update status to expired
      await Transaction.updateStatus(orderId, 'expired');
      return res.status(400).json({ error: 'Transaction has expired' });
    }

    console.log('✅ Transaction is valid, returning payment details');

    res.json({
      success: true,
      transaction_id: transaction.id,
      order_id: transaction.order_id,
      snap_token: transaction.midtrans_token,
      redirect_url: transaction.midtrans_redirect_url,
      amount_idr: transaction.amount_idr,
      amount_formatted: midtransService.formatIDR(transaction.amount_idr),
      plan_name: transaction.plan_name,
      expires_in_minutes: Math.max(0, Math.floor(10 - diffMinutes))
    });

  } catch (error) {
    console.error('Continue payment error:', error);
    res.status(500).json({ error: 'Failed to continue payment' });
  }
});

// Get user's pending transactions
router.get('/pending', isAuthenticated, async (req, res) => {
  try {
    const pendingTransactions = await Transaction.getUserPendingTransactions(req.session.userId);

    const formattedTransactions = pendingTransactions.map(transaction => {
      const createdAt = new Date(transaction.created_at);
      const now = new Date();
      const diffMinutes = (now - createdAt) / (1000 * 60);
      const expiresInMinutes = Math.max(0, Math.floor(10 - diffMinutes));

      return {
        id: transaction.id,
        order_id: transaction.order_id,
        plan_name: transaction.plan_name,
        amount_idr: transaction.amount_idr,
        amount_formatted: midtransService.formatIDR(transaction.amount_idr),
        created_at: transaction.created_at,
        expires_in_minutes: expiresInMinutes,
        is_expired: expiresInMinutes <= 0
      };
    });

    res.json({
      success: true,
      transactions: formattedTransactions
    });

  } catch (error) {
    console.error('Get pending transactions error:', error);
    res.status(500).json({ error: 'Failed to get pending transactions' });
  }
});

// Helper function to activate subscription
async function activateSubscription(transaction) {
  try {
    console.log('🎯 Activating subscription with prorated upgrade logic...');

    // Use prorated upgrade logic to create subscription
    await Subscription.createProratedUpgrade(
      transaction.user_id,
      transaction.plan_id,
      transaction.order_id
    );

    console.log('✅ Prorated subscription activated successfully');

  } catch (error) {
    console.error('Failed to activate subscription:', error);
    throw error;
  }
}

module.exports = router;
